[2025-07-07 16:08:26,186] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:Configuration:Configuration:


experiment_name: mistral-cybersecurity-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - cybersecurity
  notes: Training Mistral MoE model for cybersecurity with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_cybercrime
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-cybersecurity_kaggle
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_cybercrime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_cybersecurity_top1
  output_dir: data/output/mistral_lora_moe_cybersecurity_top1
experiment_name: mistral-cybersecurity-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - cybersecurity
  notes: Training Mistral MoE model for cybersecurity with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_cybercrime
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-cybersecurity_kaggle
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_cybercrime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_cybersecurity_top1
  output_dir: data/output/mistral_lora_moe_cybersecurity_top1
experiment_name: mistral-cybersecurity-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - cybersecurity
  notes: Training Mistral MoE model for cybersecurity with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_cybercrime
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-cybersecurity_kaggle
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_cybercrime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_cybersecurity_top1
  output_dir: data/output/mistral_lora_moe_cybersecurity_top1



[2025-07-07 16:08:39,294] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 16:08:39,310] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 16:08:39,321] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
Configuration:
experiment_name: mistral-cybersecurity-moe
seed: 42
wandb:
  enabled: true
  project: mistral-moe-training
  run_name: null
  tags:
  - mistral
  - moe
  - cybersecurity
  notes: Training Mistral MoE model for cybersecurity with router-only training
model:
  model_type: mistral
  num_experts_per_tok: 1
  base_model: mistralai/Mistral-7B-v0.1
  experts:
  - expert_name: adapter_1
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-unsafe_cybercrime
  - expert_name: adapter_2
    model_id: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/loras/mistral-lora-cybersecurity_kaggle
training:
  per_device_train_batch_size: 1
  per_device_eval_batch_size: 1
  learning_rate: 0.0001
  save_total_limit: 1
  num_train_epochs: 10
  eval_steps: 5000
  logging_strategy: steps
  logging_steps: 25
  gradient_accumulation_steps: 4
  bf16: true
  logging_first_step: true
  evaluation_strategy: steps
  save_strategy: steps
  save_steps: 5000
  load_best_model_at_end: true
  metric_for_best_model: eval_loss
  greater_is_better: false
  max_seq_length: 512
data:
  dataset_name: /home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/polished_safe_data/train_safe_gpt4_qtype2_cybercrime_polished.json
  test_size: 0.1
  test_valid_split: 0.7
paths:
  model_checkpoint: data/mistral_lora_moe_cybersecurity_top1
  output_dir: data/output/mistral_lora_moe_cybersecurity_top1

[2025-07-07 16:08:39,767] [INFO] [real_accelerator.py:219:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-07 16:08:42,097] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 16:08:42,110] [INFO] [comm.py:652:init_distributed] cdb=None
[2025-07-07 16:08:42,140] [INFO] [comm.py:652:init_distributed] cdb=None
Creating model checkpoint...
[2025-07-07 16:08:42,463] [INFO] [comm.py:652:init_distributed] cdb=None
MoE Layer Index : [*]Creating model checkpoint...[2025-07-07 16:08:42,475] [INFO] [comm.py:683:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl


Creating model checkpoint...MoE Layer Index : [*]

MoE Layer Index : [*]
Creating model checkpoint...
MoE Layer Index : [*]
[2025-07-07 16:09:06,570][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 16:09:06,603][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 16:09:06,607][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
[2025-07-07 16:09:06,608][peft.tuners.tuners_utils][INFO] - Already found a `peft_config` attribute in the model. This will lead to having multiple adapters in the model. Make sure to know what you are doing!
count_averaged_layers : 195
count_router_layers : 480
count_total_router_layers : 480
count_averaged_layers : 195count_averaged_layers : 195

count_averaged_layers : 195count_router_layers : 480
count_router_layers : 480

count_total_router_layers : 480
count_total_router_layers : 480
count_router_layers : 480
count_total_router_layers : 480
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_cybersecurity_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_cybersecurity_top1
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_cybersecurity_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_cybersecurity_top1
Loading model...
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_cybersecurity_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_cybersecurity_top1
The model is bigger than the maximum size per checkpoint (9GB) and is going to be split in 2 checkpoint shards. You can find where each parameters has been saved in the index located at data/mistral_lora_moe_cybersecurity_top1/model.safetensors.index.json.
checkpoint saved at data/mistral_lora_moe_cybersecurity_top1
Total weights: 771, Router weights: 256
Loading dataset...
Dataset sizes - Train: 5834, Val: 194, Test: 455
Total weights: 771, Router weights: 256Total weights: 771, Router weights: 256
Loading dataset...
Loading dataset...Total weights: 771, Router weights: 256


Loading dataset...
Dataset sizes - Train: 5834, Val: 194, Test: 455
Dataset sizes - Train: 5834, Val: 194, Test: 455
Dataset sizes - Train: 5834, Val: 194, Test: 455
cn-g020:2855407:2855407 [0] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.30<0>
cn-g020:2855407:2855407 [0] NCCL INFO cudaDriverVersion 12020
cn-g020:2855409:2855409 [2] NCCL INFO cudaDriverVersion 12020
cn-g020:2855408:2855408 [1] NCCL INFO cudaDriverVersion 12020
cn-g020:2855410:2855410 [3] NCCL INFO cudaDriverVersion 12020
cn-g020:2855407:2855407 [0] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g020:2855409:2855409 [2] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.30<0>
cn-g020:2855408:2855408 [1] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.30<0>
cn-g020:2855410:2855410 [3] NCCL INFO Bootstrap: Using ibp37s0:10.20.9.30<0>
cn-g020:2855410:2855410 [3] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g020:2855407:2855407 [0] NCCL INFO Comm config Blocking set to 1
cn-g020:2855409:2855409 [2] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g020:2855408:2855408 [1] NCCL INFO NCCL version 2.26.2+cuda12.2
cn-g020:2855410:2855410 [3] NCCL INFO Comm config Blocking set to 1
cn-g020:2855409:2855409 [2] NCCL INFO Comm config Blocking set to 1
cn-g020:2855408:2855408 [1] NCCL INFO Comm config Blocking set to 1
cn-g020:2855407:2856162 [0] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g020:2855408:2856165 [1] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g020:2855410:2856163 [3] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g020:2855409:2856164 [2] NCCL INFO NET/Plugin: Could not find: libnccl-net.so. Using internal net plugin.
cn-g020:2855408:2856165 [1] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.30<0>
cn-g020:2855407:2856162 [0] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.30<0>
cn-g020:2855408:2856165 [1] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g020:2855408:2856165 [1] NCCL INFO Using network IB
cn-g020:2855407:2856162 [0] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g020:2855407:2856162 [0] NCCL INFO Using network IB
cn-g020:2855409:2856164 [2] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.30<0>
cn-g020:2855410:2856163 [3] NCCL INFO NET/IB : Using [0]mlx5_0:1/IB [1]mlx5_1:1/IB [RO]; OOB ibp37s0:10.20.9.30<0>
cn-g020:2855408:2856165 [1] NCCL INFO ncclCommInitRankConfig comm 0x55a4196ba900 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xd36f3540f677f7f7 - Init START
cn-g020:2855407:2856162 [0] NCCL INFO ncclCommInitRankConfig comm 0x555719b9aa00 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xd36f3540f677f7f7 - Init START
cn-g020:2855409:2856164 [2] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g020:2855409:2856164 [2] NCCL INFO Using network IB
cn-g020:2855410:2856163 [3] NCCL INFO PROFILER/Plugin: Could not find: libnccl-profiler.so. 
cn-g020:2855410:2856163 [3] NCCL INFO Using network IB
cn-g020:2855409:2856164 [2] NCCL INFO ncclCommInitRankConfig comm 0x565580e41840 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xd36f3540f677f7f7 - Init START
cn-g020:2855410:2856163 [3] NCCL INFO ncclCommInitRankConfig comm 0x55b98487e380 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xd36f3540f677f7f7 - Init START
cn-g020:2855409:2856164 [2] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g020:2855408:2856165 [1] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g020:2855407:2856162 [0] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g020:2855410:2856163 [3] NCCL INFO RAS client listening socket at 127.0.0.1<28028>
cn-g020:2855409:2856164 [2] NCCL INFO Bootstrap timings total 0.038743 (create 0.000049, send 0.000166, recv 0.007954, ring 0.021721, delay 0.000000)
cn-g020:2855407:2856162 [0] NCCL INFO Bootstrap timings total 0.070453 (create 0.000057, send 0.000161, recv 0.000106, ring 0.005903, delay 0.000001)
cn-g020:2855410:2856163 [3] NCCL INFO Bootstrap timings total 0.031152 (create 0.000050, send 0.000362, recv 0.000189, ring 0.000057, delay 0.000000)
cn-g020:2855408:2856165 [1] NCCL INFO Bootstrap timings total 0.077473 (create 0.000049, send 0.000154, recv 0.038816, ring 0.014603, delay 0.000001)
cn-g020:2855409:2856164 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g020:2855407:2856162 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g020:2855410:2856163 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g020:2855408:2856165 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g020:2855408:2856165 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g020:2855408:2856165 [1] NCCL INFO comm 0x55a4196ba900 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g020:2855409:2856164 [2] NCCL INFO comm 0x565580e41840 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g020:2855407:2856162 [0] NCCL INFO comm 0x555719b9aa00 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g020:2855410:2856163 [3] NCCL INFO comm 0x55b98487e380 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g020:2855408:2856165 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g020:2855408:2856165 [1] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855409:2856164 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g020:2855409:2856164 [2] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855407:2856162 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g020:2855407:2856162 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g020:2855410:2856163 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g020:2855410:2856163 [3] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855408:2856178 [1] NCCL INFO [Proxy Service] Device 1 CPU core 6
cn-g020:2855409:2856180 [2] NCCL INFO [Proxy Service] Device 2 CPU core 10
cn-g020:2855407:2856162 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g020:2855407:2856162 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g020:2855408:2856179 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 2
cn-g020:2855410:2856182 [3] NCCL INFO [Proxy Service] Device 3 CPU core 2
cn-g020:2855409:2856181 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 14
cn-g020:2855407:2856162 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g020:2855407:2856162 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g020:2855408:2856165 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855410:2856183 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 12
cn-g020:2855409:2856164 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855409:2856164 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855407:2856162 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g020:2855407:2856162 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g020:2855408:2856165 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855410:2856163 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855410:2856163 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855407:2856162 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g020:2855407:2856162 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g020:2855407:2856162 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g020:2855407:2856162 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g020:2855407:2856162 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g020:2855407:2856162 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g020:2855407:2856162 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g020:2855407:2856162 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g020:2855407:2856162 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g020:2855407:2856162 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g020:2855407:2856162 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g020:2855407:2856162 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g020:2855407:2856162 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g020:2855407:2856162 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g020:2855407:2856162 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g020:2855407:2856162 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g020:2855407:2856162 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g020:2855407:2856162 [0] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855407:2856162 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g020:2855407:2856184 [0] NCCL INFO [Proxy Service] Device 0 CPU core 1
cn-g020:2855407:2856185 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 8
cn-g020:2855407:2856162 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855407:2856162 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855407:2856162 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g020:2855407:2856162 [0] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g020:2855409:2856164 [2] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g020:2855409:2856164 [2] NCCL INFO ncclCommInitRankConfig comm 0x565580e41840 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0xd36f3540f677f7f7 - Init COMPLETE
cn-g020:2855408:2856165 [1] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g020:2855408:2856165 [1] NCCL INFO ncclCommInitRankConfig comm 0x55a4196ba900 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0xd36f3540f677f7f7 - Init COMPLETE
cn-g020:2855410:2856163 [3] NCCL INFO TUNER/Plugin: Could not find: libnccl-tuner.so. Using internal tuner plugin.
cn-g020:2855410:2856163 [3] NCCL INFO ncclCommInitRankConfig comm 0x55b98487e380 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0xd36f3540f677f7f7 - Init COMPLETE
cn-g020:2855407:2856162 [0] NCCL INFO ncclCommInitRankConfig comm 0x555719b9aa00 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0xd36f3540f677f7f7 - Init COMPLETE
cn-g020:2855407:2856162 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 0.83 (kernels 0.16, alloc 0.08, bootstrap 0.08, allgathers 0.35, topo 0.06, graphs 0.00, connections 0.03, rest 0.07)
cn-g020:2855409:2856164 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.82 (kernels 0.14, alloc 0.11, bootstrap 0.05, allgathers 0.05, topo 0.06, graphs 0.00, connections 0.11, rest 0.29)
cn-g020:2855408:2856165 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.82 (kernels 0.12, alloc 0.07, bootstrap 0.10, allgathers 0.01, topo 0.06, graphs 0.00, connections 0.15, rest 0.31)
cn-g020:2855410:2856163 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.85 (kernels 0.15, alloc 0.12, bootstrap 0.05, allgathers 0.04, topo 0.06, graphs 0.01, connections 0.12, rest 0.30)
cn-g020:2855407:2856189 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856192 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856190 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856189 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856191 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g020:2855407:2856189 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g020:2855410:2856192 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g020:2855409:2856190 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Model parameters - Total: 7,356,420,096, Trainable: 1,918,238,720
Trainable percentage: 26.08%
Starting training...
[2025-07-07 16:10:44,230] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.15.4, git-hash=unknown, git-branch=unknown
[2025-07-07 16:10:44,236] [INFO] [config.py:733:__init__] Config mesh_device None world_size = 4
cn-g020:2855407:2855407 [0] NCCL INFO Comm config Blocking set to 1
cn-g020:2855407:2856255 [0] NCCL INFO Using network IB
cn-g020:2855407:2856255 [0] NCCL INFO ncclCommInitRankConfig comm 0x5556fd7d8480 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x487217e4a1b6fbb - Init START
Evaluation strategy: steps
Eval steps: 5000
Metric for best model: eval_loss
Evaluation strategy: steps
Eval steps: 5000Evaluation strategy: steps
Metric for best model: eval_loss

Eval steps: 5000
Metric for best model: eval_loss
cn-g020:2855408:2855408 [1] NCCL INFO Comm config Blocking set to 1
cn-g020:2855408:2856267 [1] NCCL INFO Using network IB
cn-g020:2855408:2856267 [1] NCCL INFO ncclCommInitRankConfig comm 0x55a41c67ab80 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x487217e4a1b6fbb - Init START
cn-g020:2855410:2855410 [3] NCCL INFO Comm config Blocking set to 1
cn-g020:2855410:2856270 [3] NCCL INFO Using network IB
cn-g020:2855409:2855409 [2] NCCL INFO Comm config Blocking set to 1
cn-g020:2855410:2856270 [3] NCCL INFO ncclCommInitRankConfig comm 0x55b98187e080 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x487217e4a1b6fbb - Init START
cn-g020:2855409:2856273 [2] NCCL INFO Using network IB
cn-g020:2855409:2856273 [2] NCCL INFO ncclCommInitRankConfig comm 0x5655816418c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x487217e4a1b6fbb - Init START
cn-g020:2855408:2856267 [1] NCCL INFO Bootstrap timings total 0.144485 (create 0.000047, send 0.000097, recv 0.143985, ring 0.000218, delay 0.000000)
cn-g020:2855410:2856270 [3] NCCL INFO Bootstrap timings total 0.015112 (create 0.000046, send 0.000098, recv 0.000074, ring 0.000109, delay 0.000000)
cn-g020:2855409:2856273 [2] NCCL INFO Bootstrap timings total 0.000707 (create 0.000061, send 0.000139, recv 0.000232, ring 0.000063, delay 0.000000)
cn-g020:2855407:2856255 [0] NCCL INFO Bootstrap timings total 3.484804 (create 0.000068, send 0.000157, recv 3.340378, ring 0.014680, delay 0.000000)
cn-g020:2855408:2856267 [1] NCCL INFO Setting affinity for GPU 1 to ffff
cn-g020:2855410:2856270 [3] NCCL INFO NVLS multicast support is not available on dev 3
cn-g020:2855407:2856255 [0] NCCL INFO NVLS multicast support is not available on dev 0
cn-g020:2855409:2856273 [2] NCCL INFO NVLS multicast support is not available on dev 2
cn-g020:2855408:2856267 [1] NCCL INFO NVLS multicast support is not available on dev 1
cn-g020:2855408:2856267 [1] NCCL INFO comm 0x55a41c67ab80 rank 1 nRanks 4 nNodes 1 localRanks 4 localRank 1 MNNVL 0
cn-g020:2855407:2856255 [0] NCCL INFO comm 0x5556fd7d8480 rank 0 nRanks 4 nNodes 1 localRanks 4 localRank 0 MNNVL 0
cn-g020:2855410:2856270 [3] NCCL INFO comm 0x55b98187e080 rank 3 nRanks 4 nNodes 1 localRanks 4 localRank 3 MNNVL 0
cn-g020:2855409:2856273 [2] NCCL INFO comm 0x5655816418c0 rank 2 nRanks 4 nNodes 1 localRanks 4 localRank 2 MNNVL 0
cn-g020:2855408:2856267 [1] NCCL INFO Trees [0] 3/-1/-1->1->2 [1] 3/-1/-1->1->2 [2] 3/-1/-1->1->2 [3] 3/-1/-1->1->2 [4] 2/-1/-1->1->0 [5] 2/-1/-1->1->0 [6] 2/-1/-1->1->0 [7] 2/-1/-1->1->0 [8] -1/-1/-1->1->3 [9] -1/-1/-1->1->3 [10] -1/-1/-1->1->3 [11] -1/-1/-1->1->3 [12] 3/-1/-1->1->2 [13] 3/-1/-1->1->2 [14] 3/-1/-1->1->2 [15] 3/-1/-1->1->2 [16] 2/-1/-1->1->0 [17] 2/-1/-1->1->0 [18] 2/-1/-1->1->0 [19] 2/-1/-1->1->0 [20] -1/-1/-1->1->3 [21] -1/-1/-1->1->3 [22] -1/-1/-1->1->3 [23] -1/-1/-1->1->3
cn-g020:2855408:2856267 [1] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855407:2856255 [0] NCCL INFO Channel 00/24 : 0 2 1 3
cn-g020:2855410:2856270 [3] NCCL INFO Trees [0] -1/-1/-1->3->1 [1] -1/-1/-1->3->1 [2] -1/-1/-1->3->1 [3] -1/-1/-1->3->1 [4] -1/-1/-1->3->2 [5] -1/-1/-1->3->2 [6] -1/-1/-1->3->2 [7] -1/-1/-1->3->2 [8] 1/-1/-1->3->0 [9] 1/-1/-1->3->0 [10] 1/-1/-1->3->0 [11] 1/-1/-1->3->0 [12] -1/-1/-1->3->1 [13] -1/-1/-1->3->1 [14] -1/-1/-1->3->1 [15] -1/-1/-1->3->1 [16] -1/-1/-1->3->2 [17] -1/-1/-1->3->2 [18] -1/-1/-1->3->2 [19] -1/-1/-1->3->2 [20] 1/-1/-1->3->0 [21] 1/-1/-1->3->0 [22] 1/-1/-1->3->0 [23] 1/-1/-1->3->0
cn-g020:2855410:2856270 [3] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855409:2856273 [2] NCCL INFO Trees [0] 1/-1/-1->2->0 [1] 1/-1/-1->2->0 [2] 1/-1/-1->2->0 [3] 1/-1/-1->2->0 [4] 3/-1/-1->2->1 [5] 3/-1/-1->2->1 [6] 3/-1/-1->2->1 [7] 3/-1/-1->2->1 [8] 0/-1/-1->2->-1 [9] 0/-1/-1->2->-1 [10] 0/-1/-1->2->-1 [11] 0/-1/-1->2->-1 [12] 1/-1/-1->2->0 [13] 1/-1/-1->2->0 [14] 1/-1/-1->2->0 [15] 1/-1/-1->2->0 [16] 3/-1/-1->2->1 [17] 3/-1/-1->2->1 [18] 3/-1/-1->2->1 [19] 3/-1/-1->2->1 [20] 0/-1/-1->2->-1 [21] 0/-1/-1->2->-1 [22] 0/-1/-1->2->-1 [23] 0/-1/-1->2->-1
cn-g020:2855409:2856273 [2] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855408:2856274 [1] NCCL INFO [Proxy Service] Device 1 CPU core 10
cn-g020:2855407:2856255 [0] NCCL INFO Channel 01/24 : 0 2 3 1
cn-g020:2855410:2856276 [3] NCCL INFO [Proxy Service] Device 3 CPU core 2
cn-g020:2855409:2856278 [2] NCCL INFO [Proxy Service] Device 2 CPU core 7
cn-g020:2855408:2856275 [1] NCCL INFO [Proxy Service UDS] Device 1 CPU core 12
cn-g020:2855407:2856255 [0] NCCL INFO Channel 02/24 : 0 1 3 2
cn-g020:2855407:2856255 [0] NCCL INFO Channel 03/24 : 0 1 2 3
cn-g020:2855410:2856277 [3] NCCL INFO [Proxy Service UDS] Device 3 CPU core 14
cn-g020:2855409:2856279 [2] NCCL INFO [Proxy Service UDS] Device 2 CPU core 3
cn-g020:2855408:2856267 [1] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855408:2856267 [1] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855407:2856255 [0] NCCL INFO Channel 04/24 : 0 3 2 1
cn-g020:2855407:2856255 [0] NCCL INFO Channel 05/24 : 0 3 1 2
cn-g020:2855407:2856255 [0] NCCL INFO Channel 06/24 : 0 2 1 3
cn-g020:2855410:2856270 [3] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855410:2856270 [3] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855409:2856273 [2] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855409:2856273 [2] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855407:2856255 [0] NCCL INFO Channel 07/24 : 0 2 3 1
cn-g020:2855407:2856255 [0] NCCL INFO Channel 08/24 : 0 1 3 2
cn-g020:2855407:2856255 [0] NCCL INFO Channel 09/24 : 0 1 2 3
cn-g020:2855407:2856255 [0] NCCL INFO Channel 10/24 : 0 3 2 1
cn-g020:2855407:2856255 [0] NCCL INFO Channel 11/24 : 0 3 1 2
cn-g020:2855407:2856255 [0] NCCL INFO Channel 12/24 : 0 2 1 3
cn-g020:2855407:2856255 [0] NCCL INFO Channel 13/24 : 0 2 3 1
cn-g020:2855407:2856255 [0] NCCL INFO Channel 14/24 : 0 1 3 2
cn-g020:2855407:2856255 [0] NCCL INFO Channel 15/24 : 0 1 2 3
cn-g020:2855407:2856255 [0] NCCL INFO Channel 16/24 : 0 3 2 1
cn-g020:2855407:2856255 [0] NCCL INFO Channel 17/24 : 0 3 1 2
cn-g020:2855407:2856255 [0] NCCL INFO Channel 18/24 : 0 2 1 3
cn-g020:2855407:2856255 [0] NCCL INFO Channel 19/24 : 0 2 3 1
cn-g020:2855407:2856255 [0] NCCL INFO Channel 20/24 : 0 1 3 2
cn-g020:2855407:2856255 [0] NCCL INFO Channel 21/24 : 0 1 2 3
cn-g020:2855407:2856255 [0] NCCL INFO Channel 22/24 : 0 3 2 1
cn-g020:2855407:2856255 [0] NCCL INFO Channel 23/24 : 0 3 1 2
cn-g020:2855407:2856255 [0] NCCL INFO Trees [0] 2/-1/-1->0->-1 [1] 2/-1/-1->0->-1 [2] 2/-1/-1->0->-1 [3] 2/-1/-1->0->-1 [4] 1/-1/-1->0->-1 [5] 1/-1/-1->0->-1 [6] 1/-1/-1->0->-1 [7] 1/-1/-1->0->-1 [8] 3/-1/-1->0->2 [9] 3/-1/-1->0->2 [10] 3/-1/-1->0->2 [11] 3/-1/-1->0->2 [12] 2/-1/-1->0->-1 [13] 2/-1/-1->0->-1 [14] 2/-1/-1->0->-1 [15] 2/-1/-1->0->-1 [16] 1/-1/-1->0->-1 [17] 1/-1/-1->0->-1 [18] 1/-1/-1->0->-1 [19] 1/-1/-1->0->-1 [20] 3/-1/-1->0->2 [21] 3/-1/-1->0->2 [22] 3/-1/-1->0->2 [23] 3/-1/-1->0->2
cn-g020:2855407:2856255 [0] NCCL INFO P2P Chunksize set to 524288
cn-g020:2855407:2856255 [0] NCCL INFO Check P2P Type intraNodeP2pSupport 1 directMode 0
cn-g020:2855407:2856280 [0] NCCL INFO [Proxy Service] Device 0 CPU core 13
cn-g020:2855407:2856281 [0] NCCL INFO [Proxy Service UDS] Device 0 CPU core 10
cn-g020:2855407:2856255 [0] NCCL INFO threadThresholds 8/8/64 | 32/8/64 | 512 | 512
cn-g020:2855407:2856255 [0] NCCL INFO 24 coll channels, 24 collnet channels, 0 nvls channels, 32 p2p channels, 8 p2p channels per peer
cn-g020:2855407:2856255 [0] NCCL INFO CC Off, workFifoBytes 1048576
cn-g020:2855407:2856255 [0] NCCL INFO ncclCommInitRankConfig comm 0x5556fd7d8480 rank 0 nranks 4 cudaDev 0 nvmlDev 0 busId 1000 commId 0x487217e4a1b6fbb - Init COMPLETE
cn-g020:2855407:2856255 [0] NCCL INFO Init timings - ncclCommInitRankConfig: rank 0 nranks 4 total 4.01 (kernels 0.00, alloc 0.01, bootstrap 3.52, allgathers 0.31, topo 0.06, graphs 0.00, connections 0.05, rest 0.06)
cn-g020:2855409:2856273 [2] NCCL INFO ncclCommInitRankConfig comm 0x5655816418c0 rank 2 nranks 4 cudaDev 2 nvmlDev 2 busId 81000 commId 0x487217e4a1b6fbb - Init COMPLETE
cn-g020:2855408:2856267 [1] NCCL INFO ncclCommInitRankConfig comm 0x55a41c67ab80 rank 1 nranks 4 cudaDev 1 nvmlDev 1 busId 41000 commId 0x487217e4a1b6fbb - Init COMPLETE
cn-g020:2855408:2856267 [1] NCCL INFO Init timings - ncclCommInitRankConfig: rank 1 nranks 4 total 0.67 (kernels 0.00, alloc 0.01, bootstrap 0.15, allgathers 0.03, topo 0.06, graphs 0.00, connections 0.11, rest 0.30)
cn-g020:2855410:2856270 [3] NCCL INFO ncclCommInitRankConfig comm 0x55b98187e080 rank 3 nranks 4 cudaDev 3 nvmlDev 3 busId c1000 commId 0x487217e4a1b6fbb - Init COMPLETE
cn-g020:2855407:2856285 [0] NCCL INFO Channel 02/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856273 [2] NCCL INFO Init timings - ncclCommInitRankConfig: rank 2 nranks 4 total 0.53 (kernels 0.00, alloc 0.02, bootstrap 0.03, allgathers 0.05, topo 0.06, graphs 0.00, connections 0.11, rest 0.27)
cn-g020:2855408:2856286 [1] NCCL INFO Channel 03/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856270 [3] NCCL INFO Init timings - ncclCommInitRankConfig: rank 3 nranks 4 total 0.54 (kernels 0.00, alloc 0.01, bootstrap 0.03, allgathers 0.06, topo 0.06, graphs 0.01, connections 0.11, rest 0.26)
cn-g020:2855407:2856285 [0] NCCL INFO Channel 03/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 01/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 05/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 00/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 08/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 03/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 09/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 03/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 09/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 07/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 11/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 06/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 14/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 09/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 15/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 09/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 15/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 13/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 17/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 12/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 20/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 15/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 21/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 15/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 21/0 : 0[0] -> 1[1] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 19/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 23/0 : 1[1] -> 2[2] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 18/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 21/0 : 2[2] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 21/0 : 3[3] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 00/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 01/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 00/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 02/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 02/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 05/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 01/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 05/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 06/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 07/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 06/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 08/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 08/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 11/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 07/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 11/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 12/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 13/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 12/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 14/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 14/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 17/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 13/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 17/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 18/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 19/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 18/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 20/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 20/0 : 1[1] -> 3[3] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 23/0 : 3[3] -> 1[1] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 19/0 : 0[0] -> 2[2] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 23/0 : 2[2] -> 0[0] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 01/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 02/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 04/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 00/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 04/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 04/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 05/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 04/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 07/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 08/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 10/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 06/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 10/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 10/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 11/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 10/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 13/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 14/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 16/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 12/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 16/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 16/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 17/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 16/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 19/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 20/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 22/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 18/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855408:2856286 [1] NCCL INFO Channel 22/0 : 1[1] -> 0[0] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Channel 22/0 : 3[3] -> 2[2] via P2P/CUMEM/read
cn-g020:2855407:2856285 [0] NCCL INFO Channel 23/0 : 0[0] -> 3[3] via P2P/CUMEM/read
cn-g020:2855409:2856287 [2] NCCL INFO Channel 22/0 : 2[2] -> 1[1] via P2P/CUMEM/read
cn-g020:2855410:2856288 [3] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g020:2855407:2856285 [0] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g020:2855409:2856287 [2] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
cn-g020:2855408:2856286 [1] NCCL INFO Connected all rings, use ring PXN 0 GDR 1
[2025-07-07 16:10:50,863] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False
[2025-07-07 16:10:50,874] [INFO] [logging.py:128:log_dist] [Rank 0] Using client Optimizer as basic optimizer
[2025-07-07 16:10:50,880] [INFO] [logging.py:128:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer
[2025-07-07 16:10:50,907] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Basic Optimizer = AdamW
[2025-07-07 16:10:50,914] [INFO] [utils.py:59:is_zero_supported_optimizer] Checking ZeRO support for optimizer=AdamW type=<class 'torch.optim.adamw.AdamW'>
[2025-07-07 16:10:50,921] [INFO] [logging.py:128:log_dist] [Rank 0] Creating torch.bfloat16 ZeRO stage 2 optimizer
[2025-07-07 16:10:50,928] [INFO] [stage_1_and_2.py:149:__init__] Reduce bucket size 5********
[2025-07-07 16:10:50,934] [INFO] [stage_1_and_2.py:150:__init__] Allgather bucket size 2********
[2025-07-07 16:10:50,941] [INFO] [stage_1_and_2.py:151:__init__] CPU Offload: False
[2025-07-07 16:10:50,948] [INFO] [stage_1_and_2.py:152:__init__] Round robin gradient partitioning: False
[2025-07-07 16:10:54,528] [INFO] [utils.py:781:see_memory_usage] Before initializing optimizer states
[2025-07-07 16:10:54,536] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.02 GB         CA 20.36 GB         Max_CA 20 GB 
[2025-07-07 16:10:54,543] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 40.35 GB, percent = 4.0%
[2025-07-07 16:10:54,711] [INFO] [utils.py:781:see_memory_usage] After initializing optimizer states
[2025-07-07 16:10:54,718] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 20.91 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-07-07 16:10:54,725] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 40.35 GB, percent = 4.0%
[2025-07-07 16:10:54,731] [INFO] [stage_1_and_2.py:544:__init__] optimizer state initialized
[2025-07-07 16:10:54,900] [INFO] [utils.py:781:see_memory_usage] After initializing ZeRO optimizer
[2025-07-07 16:10:54,907] [INFO] [utils.py:782:see_memory_usage] MA 19.12 GB         Max_MA 19.12 GB         CA 22.15 GB         Max_CA 22 GB 
[2025-07-07 16:10:54,912] [INFO] [utils.py:789:see_memory_usage] CPU Virtual Memory:  used = 40.35 GB, percent = 4.0%
[2025-07-07 16:10:54,919] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Final Optimizer = DeepSpeedZeroOptimizer
[2025-07-07 16:10:54,924] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = None
[2025-07-07 16:10:54,931] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed LR Scheduler = None
[2025-07-07 16:10:54,937] [INFO] [logging.py:128:log_dist] [Rank 0] step=0, skipped=0, lr=[0.0001], mom=[(0.9, 0.999)]
[2025-07-07 16:10:54,946] [INFO] [config.py:999:print] DeepSpeedEngine configuration:
[2025-07-07 16:10:54,953] [INFO] [config.py:1003:print]   activation_checkpointing_config  {
    "partition_activations": false, 
    "contiguous_memory_optimization": false, 
    "cpu_checkpointing": false, 
    "number_checkpoints": null, 
    "synchronize_checkpoint_boundary": false, 
    "profile": false
}
[2025-07-07 16:10:54,958] [INFO] [config.py:1003:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'thread_count': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}
[2025-07-07 16:10:54,964] [INFO] [config.py:1003:print]   amp_enabled .................. False
[2025-07-07 16:10:54,968] [INFO] [config.py:1003:print]   amp_params ................... False
[2025-07-07 16:10:54,974] [INFO] [config.py:1003:print]   autotuning_config ............ {
    "enabled": false, 
    "start_step": null, 
    "end_step": null, 
    "metric_path": null, 
    "arg_mappings": null, 
    "metric": "throughput", 
    "model_info": null, 
    "results_dir": "autotuning_results", 
    "exps_dir": "autotuning_exps", 
    "overwrite": true, 
    "fast": true, 
    "start_profile_step": 3, 
    "end_profile_step": 5, 
    "tuner_type": "gridsearch", 
    "tuner_early_stopping": 5, 
    "tuner_num_trials": 50, 
    "model_info_path": null, 
    "mp_size": 1, 
    "max_train_batch_size": null, 
    "min_train_batch_size": 1, 
    "max_train_micro_batch_size_per_gpu": 1.024000e+03, 
    "min_train_micro_batch_size_per_gpu": 1, 
    "num_tuning_micro_batch_sizes": 3
}
[2025-07-07 16:10:54,980] [INFO] [config.py:1003:print]   bfloat16_enabled ............. True
[2025-07-07 16:10:54,986] [INFO] [config.py:1003:print]   bfloat16_immediate_grad_update  False
[2025-07-07 16:10:54,993] [INFO] [config.py:1003:print]   checkpoint_parallel_write_pipeline  False
[2025-07-07 16:10:55,000] [INFO] [config.py:1003:print]   checkpoint_tag_validation_enabled  True
[2025-07-07 16:10:55,007] [INFO] [config.py:1003:print]   checkpoint_tag_validation_fail  False
[2025-07-07 16:10:55,013] [INFO] [config.py:1003:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f02a0945de0>
[2025-07-07 16:10:55,019] [INFO] [config.py:1003:print]   communication_data_type ...... None
[2025-07-07 16:10:55,027] [INFO] [config.py:1003:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}
[2025-07-07 16:10:55,032] [INFO] [config.py:1003:print]   curriculum_enabled_legacy .... False
[2025-07-07 16:10:55,038] [INFO] [config.py:1003:print]   curriculum_params_legacy ..... False
[2025-07-07 16:10:55,045] [INFO] [config.py:1003:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}
[2025-07-07 16:10:55,051] [INFO] [config.py:1003:print]   data_efficiency_enabled ...... False
[2025-07-07 16:10:55,056] [INFO] [config.py:1003:print]   dataloader_drop_last ......... False
[2025-07-07 16:10:55,063] [INFO] [config.py:1003:print]   disable_allgather ............ False
[2025-07-07 16:10:55,070] [INFO] [config.py:1003:print]   dump_state ................... False
[2025-07-07 16:10:55,077] [INFO] [config.py:1003:print]   dynamic_loss_scale_args ...... None
[2025-07-07 16:10:55,084] [INFO] [config.py:1003:print]   eigenvalue_enabled ........... False
[2025-07-07 16:10:55,092] [INFO] [config.py:1003:print]   eigenvalue_gas_boundary_resolution  1
[2025-07-07 16:10:55,097] [INFO] [config.py:1003:print]   eigenvalue_layer_name ........ bert.encoder.layer
[2025-07-07 16:10:55,103] [INFO] [config.py:1003:print]   eigenvalue_layer_num ......... 0
[2025-07-07 16:10:55,110] [INFO] [config.py:1003:print]   eigenvalue_max_iter .......... 100
[2025-07-07 16:10:55,117] [INFO] [config.py:1003:print]   eigenvalue_stability ......... 1e-06
[2025-07-07 16:10:55,124] [INFO] [config.py:1003:print]   eigenvalue_tol ............... 0.01
[2025-07-07 16:10:55,131] [INFO] [config.py:1003:print]   eigenvalue_verbose ........... False
[2025-07-07 16:10:55,138] [INFO] [config.py:1003:print]   elasticity_enabled ........... False
[2025-07-07 16:10:55,144] [INFO] [config.py:1003:print]   flops_profiler_config ........ {
    "enabled": false, 
    "recompute_fwd_factor": 0.0, 
    "profile_step": 1, 
    "module_depth": -1, 
    "top_modules": 1, 
    "detailed": true, 
    "output_file": null
}
[2025-07-07 16:10:55,150] [INFO] [config.py:1003:print]   fp16_auto_cast ............... None
[2025-07-07 16:10:55,157] [INFO] [config.py:1003:print]   fp16_enabled ................. False
[2025-07-07 16:10:55,163] [INFO] [config.py:1003:print]   fp16_master_weights_and_gradients  False
[2025-07-07 16:10:55,167] [INFO] [config.py:1003:print]   global_rank .................. 0
[2025-07-07 16:10:55,172] [INFO] [config.py:1003:print]   grad_accum_dtype ............. None
[2025-07-07 16:10:55,177] [INFO] [config.py:1003:print]   gradient_accumulation_steps .. 4
[2025-07-07 16:10:55,183] [INFO] [config.py:1003:print]   gradient_clipping ............ 1.0
[2025-07-07 16:10:55,189] [INFO] [config.py:1003:print]   gradient_predivide_factor .... 1.0
[2025-07-07 16:10:55,195] [INFO] [config.py:1003:print]   graph_harvesting ............. False
[2025-07-07 16:10:55,200] [INFO] [config.py:1003:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8
[2025-07-07 16:10:55,206] [INFO] [config.py:1003:print]   initial_dynamic_scale ........ 1
[2025-07-07 16:10:55,210] [INFO] [config.py:1003:print]   load_universal_checkpoint .... False
[2025-07-07 16:10:55,216] [INFO] [config.py:1003:print]   loss_scale ................... 1.0
[2025-07-07 16:10:55,223] [INFO] [config.py:1003:print]   memory_breakdown ............. False
[2025-07-07 16:10:55,230] [INFO] [config.py:1003:print]   mics_hierarchial_params_gather  False
[2025-07-07 16:10:55,235] [INFO] [config.py:1003:print]   mics_shard_size .............. -1
[2025-07-07 16:10:55,241] [INFO] [config.py:1003:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')
[2025-07-07 16:10:55,247] [INFO] [config.py:1003:print]   nebula_config ................ {
    "enabled": false, 
    "persistent_storage_path": null, 
    "persistent_time_interval": 100, 
    "num_of_version_in_retention": 2, 
    "enable_nebula_load": true, 
    "load_path": null
}
[2025-07-07 16:10:55,253] [INFO] [config.py:1003:print]   optimizer_legacy_fusion ...... False
[2025-07-07 16:10:55,259] [INFO] [config.py:1003:print]   optimizer_name ............... None
[2025-07-07 16:10:55,264] [INFO] [config.py:1003:print]   optimizer_params ............. None
[2025-07-07 16:10:55,269] [INFO] [config.py:1003:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}
[2025-07-07 16:10:55,274] [INFO] [config.py:1003:print]   pld_enabled .................. False
[2025-07-07 16:10:55,280] [INFO] [config.py:1003:print]   pld_params ................... False
[2025-07-07 16:10:55,285] [INFO] [config.py:1003:print]   prescale_gradients ........... False
[2025-07-07 16:10:55,290] [INFO] [config.py:1003:print]   scheduler_name ............... None
[2025-07-07 16:10:55,296] [INFO] [config.py:1003:print]   scheduler_params ............. None
[2025-07-07 16:10:55,302] [INFO] [config.py:1003:print]   seq_parallel_communication_data_type  torch.float32
[2025-07-07 16:10:55,310] [INFO] [config.py:1003:print]   sparse_attention ............. None
[2025-07-07 16:10:55,316] [INFO] [config.py:1003:print]   sparse_gradients_enabled ..... False
[2025-07-07 16:10:55,322] [INFO] [config.py:1003:print]   steps_per_print .............. inf
[2025-07-07 16:10:55,328] [INFO] [config.py:1003:print]   timers_config ................ enabled=True synchronized=True
[2025-07-07 16:10:55,333] [INFO] [config.py:1003:print]   train_batch_size ............. 16
[2025-07-07 16:10:55,339] [INFO] [config.py:1003:print]   train_micro_batch_size_per_gpu  1
[2025-07-07 16:10:55,345] [INFO] [config.py:1003:print]   use_data_before_expert_parallel_  False
[2025-07-07 16:10:55,350] [INFO] [config.py:1003:print]   use_node_local_storage ....... False
[2025-07-07 16:10:55,356] [INFO] [config.py:1003:print]   wall_clock_breakdown ......... False
[2025-07-07 16:10:55,362] [INFO] [config.py:1003:print]   weight_quantization_config ... None
[2025-07-07 16:10:55,368] [INFO] [config.py:1003:print]   world_size ................... 4
[2025-07-07 16:10:55,374] [INFO] [config.py:1003:print]   zero_allow_untested_optimizer  True
[2025-07-07 16:10:55,382] [INFO] [config.py:1003:print]   zero_config .................. stage=2 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=5******** use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=2******** overlap_comm=True load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1********0 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1********0 max_reuse_distance=1********0 gather_16bit_weights_on_model_save=False use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True
[2025-07-07 16:10:55,390] [INFO] [config.py:1003:print]   zero_enabled ................. True
[2025-07-07 16:10:55,396] [INFO] [config.py:1003:print]   zero_force_ds_cpu_optimizer .. True
[2025-07-07 16:10:55,402] [INFO] [config.py:1003:print]   zero_optimization_stage ...... 2
[2025-07-07 16:10:55,409] [INFO] [config.py:989:print_user_config]   json = {
    "zero_optimization": {
        "stage": 2, 
        "allgather_partitions": true, 
        "allgather_bucket_size": 2.000000e+08, 
        "overlap_comm": true, 
        "reduce_scatter": true, 
        "contiguous_gradients": true
    }, 
    "gradient_accumulation_steps": 4, 
    "gradient_clipping": 1.0, 
    "steps_per_print": inf, 
    "train_batch_size": 16, 
    "train_micro_batch_size_per_gpu": 1, 
    "wall_clock_breakdown": false, 
    "bf16": {
        "enabled": true
    }, 
    "fp16": {
        "enabled": false
    }, 
    "zero_allow_untested_optimizer": true
}
{'loss': 1.4346, 'grad_norm': 16.38957405090332, 'learning_rate': 9.997252747252747e-05, 'epoch': 0.0}
{'loss': 1.417, 'grad_norm': 1.8148913383483887, 'learning_rate': 9.931318681318682e-05, 'epoch': 0.07}
{'loss': 1.2993, 'grad_norm': 1.712958812713623, 'learning_rate': 9.862637362637364e-05, 'epoch': 0.14}
{'loss': 1.227, 'grad_norm': 1.5268690586090088, 'learning_rate': 9.793956043956044e-05, 'epoch': 0.21}
{'loss': 1.196, 'grad_norm': 1.7029457092285156, 'learning_rate': 9.725274725274725e-05, 'epoch': 0.27}
{'loss': 1.1358, 'grad_norm': 1.3167990446090698, 'learning_rate': 9.656593406593408e-05, 'epoch': 0.34}
{'loss': 1.1013, 'grad_norm': 1.589208960533142, 'learning_rate': 9.587912087912089e-05, 'epoch': 0.41}
{'loss': 1.0871, 'grad_norm': 1.335816740989685, 'learning_rate': 9.519230769230769e-05, 'epoch': 0.48}
{'loss': 1.0638, 'grad_norm': 1.3912709951400757, 'learning_rate': 9.450549450549451e-05, 'epoch': 0.55}
{'loss': 1.0548, 'grad_norm': 1.4673134088516235, 'learning_rate': 9.381868131868132e-05, 'epoch': 0.62}
{'loss': 0.9972, 'grad_norm': 1.7384017705917358, 'learning_rate': 9.313186813186814e-05, 'epoch': 0.69}
{'loss': 0.9587, 'grad_norm': 1.2966548204421997, 'learning_rate': 9.244505494505495e-05, 'epoch': 0.75}
{'loss': 0.9176, 'grad_norm': 1.488337755203247, 'learning_rate': 9.175824175824176e-05, 'epoch': 0.82}
{'loss': 0.8874, 'grad_norm': 1.3563426733016968, 'learning_rate': 9.107142857142857e-05, 'epoch': 0.89}
{'loss': 0.8963, 'grad_norm': 1.2060751914978027, 'learning_rate': 9.038461538461538e-05, 'epoch': 0.96}
{'loss': 0.8407, 'grad_norm': 1.196905255317688, 'learning_rate': 8.969780219780221e-05, 'epoch': 1.03}
{'loss': 0.7578, 'grad_norm': 1.1231657266616821, 'learning_rate': 8.901098901098901e-05, 'epoch': 1.1}
{'loss': 0.7165, 'grad_norm': 1.2288506031036377, 'learning_rate': 8.832417582417582e-05, 'epoch': 1.17}
{'loss': 0.7123, 'grad_norm': 1.1489005088806152, 'learning_rate': 8.763736263736264e-05, 'epoch': 1.23}
{'loss': 0.6708, 'grad_norm': 1.1777037382125854, 'learning_rate': 8.695054945054946e-05, 'epoch': 1.3}
{'loss': 0.6731, 'grad_norm': 1.0641660690307617, 'learning_rate': 8.626373626373627e-05, 'epoch': 1.37}
{'loss': 0.6632, 'grad_norm': 1.2704219818115234, 'learning_rate': 8.557692307692308e-05, 'epoch': 1.44}
{'loss': 0.6462, 'grad_norm': 1.0936976671218872, 'learning_rate': 8.489010989010989e-05, 'epoch': 1.51}
{'loss': 0.6619, 'grad_norm': 1.1402714252471924, 'learning_rate': 8.42032967032967e-05, 'epoch': 1.58}
{'loss': 0.6221, 'grad_norm': 1.1004263162612915, 'learning_rate': 8.351648351648353e-05, 'epoch': 1.65}
{'loss': 0.6346, 'grad_norm': 1.2553528547286987, 'learning_rate': 8.282967032967034e-05, 'epoch': 1.71}
{'loss': 0.6234, 'grad_norm': 1.127528190612793, 'learning_rate': 8.214285714285714e-05, 'epoch': 1.78}
{'loss': 0.6104, 'grad_norm': 1.064731240272522, 'learning_rate': 8.145604395604396e-05, 'epoch': 1.85}
{'loss': 0.5965, 'grad_norm': 1.049346685409546, 'learning_rate': 8.076923076923078e-05, 'epoch': 1.92}
{'loss': 0.5417, 'grad_norm': 1.1425535678863525, 'learning_rate': 8.008241758241759e-05, 'epoch': 1.99}
{'loss': 0.476, 'grad_norm': 1.1548168659210205, 'learning_rate': 7.93956043956044e-05, 'epoch': 2.06}
{'loss': 0.4359, 'grad_norm': 1.0676974058151245, 'learning_rate': 7.870879120879121e-05, 'epoch': 2.13}
{'loss': 0.4471, 'grad_norm': 1.1030092239379883, 'learning_rate': 7.802197802197802e-05, 'epoch': 2.19}
{'loss': 0.4358, 'grad_norm': 1.019608497619629, 'learning_rate': 7.733516483516484e-05, 'epoch': 2.26}
{'loss': 0.4539, 'grad_norm': 1.1977897882461548, 'learning_rate': 7.664835164835166e-05, 'epoch': 2.33}
{'loss': 0.4496, 'grad_norm': 1.1425317525863647, 'learning_rate': 7.596153846153846e-05, 'epoch': 2.4}
{'loss': 0.4236, 'grad_norm': 1.111838936805725, 'learning_rate': 7.527472527472527e-05, 'epoch': 2.47}
{'loss': 0.4052, 'grad_norm': 1.0223907232284546, 'learning_rate': 7.45879120879121e-05, 'epoch': 2.54}
{'loss': 0.394, 'grad_norm': 0.9875006675720215, 'learning_rate': 7.390109890109891e-05, 'epoch': 2.61}
{'loss': 0.3645, 'grad_norm': 0.8957262635231018, 'learning_rate': 7.321428571428571e-05, 'epoch': 2.67}
{'loss': 0.38, 'grad_norm': 0.8985794186592102, 'learning_rate': 7.252747252747253e-05, 'epoch': 2.74}
{'loss': 0.3799, 'grad_norm': 1.2053546905517578, 'learning_rate': 7.184065934065934e-05, 'epoch': 2.81}
{'loss': 0.4044, 'grad_norm': 1.0474448204040527, 'learning_rate': 7.115384615384616e-05, 'epoch': 2.88}
{'loss': 0.3522, 'grad_norm': 0.8898846507072449, 'learning_rate': 7.046703296703298e-05, 'epoch': 2.95}
{'loss': 0.2944, 'grad_norm': 0.757949709892273, 'learning_rate': 6.978021978021978e-05, 'epoch': 3.02}
{'loss': 0.2869, 'grad_norm': 0.807428240776062, 'learning_rate': 6.909340659340659e-05, 'epoch': 3.09}
{'loss': 0.2914, 'grad_norm': 0.7923932671546936, 'learning_rate': 6.840659340659342e-05, 'epoch': 3.15}
{'loss': 0.2866, 'grad_norm': 1.5460729598999023, 'learning_rate': 6.771978021978023e-05, 'epoch': 3.22}
{'loss': 0.2585, 'grad_norm': 1.0203114748001099, 'learning_rate': 6.703296703296703e-05, 'epoch': 3.29}
{'loss': 0.2724, 'grad_norm': 0.7529361844062805, 'learning_rate': 6.634615384615385e-05, 'epoch': 3.36}
{'loss': 0.2567, 'grad_norm': 0.9605937600135803, 'learning_rate': 6.565934065934066e-05, 'epoch': 3.43}
{'loss': 0.2388, 'grad_norm': 0.7872242331504822, 'learning_rate': 6.497252747252748e-05, 'epoch': 3.5}
{'loss': 0.2475, 'grad_norm': 0.9850013256072998, 'learning_rate': 6.428571428571429e-05, 'epoch': 3.57}
{'loss': 0.2562, 'grad_norm': 0.8061688542366028, 'learning_rate': 6.35989010989011e-05, 'epoch': 3.63}
{'loss': 0.2197, 'grad_norm': 0.889859676361084, 'learning_rate': 6.291208791208791e-05, 'epoch': 3.7}
{'loss': 0.2292, 'grad_norm': 0.9450198411941528, 'learning_rate': 6.222527472527472e-05, 'epoch': 3.77}
{'loss': 0.235, 'grad_norm': 0.8541351556777954, 'learning_rate': 6.153846153846155e-05, 'epoch': 3.84}
{'loss': 0.2306, 'grad_norm': 0.8516114354133606, 'learning_rate': 6.085164835164835e-05, 'epoch': 3.91}
{'loss': 0.2198, 'grad_norm': 0.8951203227043152, 'learning_rate': 6.0164835164835166e-05, 'epoch': 3.98}
{'loss': 0.1956, 'grad_norm': 0.7352474331855774, 'learning_rate': 5.947802197802198e-05, 'epoch': 4.05}
{'loss': 0.1712, 'grad_norm': 0.8153177499771118, 'learning_rate': 5.8791208791208796e-05, 'epoch': 4.12}
{'loss': 0.1713, 'grad_norm': 0.7238132357597351, 'learning_rate': 5.8104395604395615e-05, 'epoch': 4.18}
{'loss': 0.1759, 'grad_norm': 1.0678848028182983, 'learning_rate': 5.741758241758241e-05, 'epoch': 4.25}
{'loss': 0.1874, 'grad_norm': 1.049028754234314, 'learning_rate': 5.673076923076923e-05, 'epoch': 4.32}
{'loss': 0.1607, 'grad_norm': 0.816953718662262, 'learning_rate': 5.604395604395605e-05, 'epoch': 4.39}
{'loss': 0.1773, 'grad_norm': 0.7971611618995667, 'learning_rate': 5.535714285714286e-05, 'epoch': 4.46}
{'loss': 0.1733, 'grad_norm': 0.8341520428657532, 'learning_rate': 5.467032967032967e-05, 'epoch': 4.53}
{'loss': 0.1646, 'grad_norm': 0.5485754013061523, 'learning_rate': 5.3983516483516486e-05, 'epoch': 4.59}
{'loss': 0.1601, 'grad_norm': 1.072706937789917, 'learning_rate': 5.32967032967033e-05, 'epoch': 4.66}
{'loss': 0.1361, 'grad_norm': 0.9501187205314636, 'learning_rate': 5.2609890109890116e-05, 'epoch': 4.73}
{'loss': 0.1396, 'grad_norm': 0.6673064231872559, 'learning_rate': 5.192307692307693e-05, 'epoch': 4.8}
{'loss': 0.1344, 'grad_norm': 0.8424892425537109, 'learning_rate': 5.123626373626373e-05, 'epoch': 4.87}
{'loss': 0.1522, 'grad_norm': 0.6498057842254639, 'learning_rate': 5.054945054945055e-05, 'epoch': 4.94}
{'loss': 0.1239, 'grad_norm': 0.6067996025085449, 'learning_rate': 4.9862637362637363e-05, 'epoch': 5.01}
{'loss': 0.1145, 'grad_norm': 0.596452534198761, 'learning_rate': 4.9175824175824175e-05, 'epoch': 5.08}
{'loss': 0.1226, 'grad_norm': 0.8038983941078186, 'learning_rate': 4.8489010989010994e-05, 'epoch': 5.14}
{'loss': 0.112, 'grad_norm': 0.9237465858459473, 'learning_rate': 4.7802197802197806e-05, 'epoch': 5.21}
{'loss': 0.1104, 'grad_norm': 0.685691773891449, 'learning_rate': 4.711538461538462e-05, 'epoch': 5.28}
{'loss': 0.0969, 'grad_norm': 0.7906137108802795, 'learning_rate': 4.642857142857143e-05, 'epoch': 5.35}
{'loss': 0.1134, 'grad_norm': 0.9515216946601868, 'learning_rate': 4.574175824175824e-05, 'epoch': 5.42}
{'loss': 0.1091, 'grad_norm': 0.6109901070594788, 'learning_rate': 4.505494505494506e-05, 'epoch': 5.49}
{'loss': 0.0903, 'grad_norm': 0.45745596289634705, 'learning_rate': 4.4368131868131865e-05, 'epoch': 5.56}
{'loss': 0.1022, 'grad_norm': 0.5319530963897705, 'learning_rate': 4.3681318681318683e-05, 'epoch': 5.62}
{'loss': 0.0914, 'grad_norm': 0.5536872744560242, 'learning_rate': 4.29945054945055e-05, 'epoch': 5.69}
{'loss': 0.1017, 'grad_norm': 0.6379515528678894, 'learning_rate': 4.230769230769231e-05, 'epoch': 5.76}
{'loss': 0.0872, 'grad_norm': 0.7562124729156494, 'learning_rate': 4.1620879120879126e-05, 'epoch': 5.83}
{'loss': 0.086, 'grad_norm': 0.4816261827945709, 'learning_rate': 4.093406593406594e-05, 'epoch': 5.9}
{'loss': 0.0894, 'grad_norm': 0.7305359244346619, 'learning_rate': 4.024725274725275e-05, 'epoch': 5.97}
{'loss': 0.0849, 'grad_norm': 0.679156482219696, 'learning_rate': 3.956043956043956e-05, 'epoch': 6.04}
{'loss': 0.0776, 'grad_norm': 0.4419774115085602, 'learning_rate': 3.887362637362637e-05, 'epoch': 6.1}
{'loss': 0.0738, 'grad_norm': 0.5111666321754456, 'learning_rate': 3.8186813186813185e-05, 'epoch': 6.17}
{'loss': 0.0809, 'grad_norm': 0.6544767022132874, 'learning_rate': 3.75********000003e-05, 'epoch': 6.24}
{'loss': 0.077, 'grad_norm': 0.41527214646339417, 'learning_rate': 3.6813186813186815e-05, 'epoch': 6.31}
{'loss': 0.0759, 'grad_norm': 0.5527138113975525, 'learning_rate': 3.612637362637363e-05, 'epoch': 6.38}
{'loss': 0.0722, 'grad_norm': 0.5223740935325623, 'learning_rate': 3.5439560439560446e-05, 'epoch': 6.45}
{'loss': 0.0648, 'grad_norm': 0.6783801317214966, 'learning_rate': 3.475274725274725e-05, 'epoch': 6.52}
{'loss': 0.0683, 'grad_norm': 3.1773462295532227, 'learning_rate': 3.406593406593407e-05, 'epoch': 6.58}
{'loss': 0.0797, 'grad_norm': 0.468190461397171, 'learning_rate': 3.337912087912088e-05, 'epoch': 6.65}
{'loss': 0.0733, 'grad_norm': 0.46463117003440857, 'learning_rate': 3.269230769230769e-05, 'epoch': 6.72}
{'loss': 0.072, 'grad_norm': 0.5627534985542297, 'learning_rate': 3.2005494505494505e-05, 'epoch': 6.79}
{'loss': 0.0676, 'grad_norm': 0.6791442632675171, 'learning_rate': 3.131868131868132e-05, 'epoch': 6.86}
{'loss': 0.0881, 'grad_norm': 0.6765139102935791, 'learning_rate': 3.0631868131868135e-05, 'epoch': 6.93}
{'loss': 0.0552, 'grad_norm': 0.27697232365608215, 'learning_rate': 2.9945054945054947e-05, 'epoch': 7.0}
{'loss': 0.0545, 'grad_norm': 0.3436700701713562, 'learning_rate': 2.9258241758241762e-05, 'epoch': 7.06}
{'loss': 0.0607, 'grad_norm': 0.49485236406326294, 'learning_rate': 2.857142857142857e-05, 'epoch': 7.13}
{'loss': 0.0496, 'grad_norm': 0.32918137311935425, 'learning_rate': 2.7884615384615386e-05, 'epoch': 7.2}
{'loss': 0.0515, 'grad_norm': 0.24723735451698303, 'learning_rate': 2.7197802197802198e-05, 'epoch': 7.27}
{'loss': 0.0566, 'grad_norm': 0.40960344672203064, 'learning_rate': 2.6510989010989013e-05, 'epoch': 7.34}
{'loss': 0.0537, 'grad_norm': 0.4395734965801239, 'learning_rate': 2.582417582417583e-05, 'epoch': 7.41}
{'loss': 0.0522, 'grad_norm': 0.6035223603248596, 'learning_rate': 2.5137362637362637e-05, 'epoch': 7.48}
{'loss': 0.0522, 'grad_norm': 0.33442965149879456, 'learning_rate': 2.4450549450549452e-05, 'epoch': 7.54}
{'loss': 0.049, 'grad_norm': 0.5305705070495605, 'learning_rate': 2.3763736263736264e-05, 'epoch': 7.61}
{'loss': 0.0487, 'grad_norm': 0.24350735545158386, 'learning_rate': 2.307692307692308e-05, 'epoch': 7.68}
{'loss': 0.0472, 'grad_norm': 0.42816999554634094, 'learning_rate': 2.239010989010989e-05, 'epoch': 7.75}
{'loss': 0.0481, 'grad_norm': 0.32598739862442017, 'learning_rate': 2.1703296703296706e-05, 'epoch': 7.82}
{'loss': 0.0433, 'grad_norm': 0.32343658804893494, 'learning_rate': 2.1016483516483518e-05, 'epoch': 7.89}
{'loss': 0.0572, 'grad_norm': 0.6456313133239746, 'learning_rate': 2.032967032967033e-05, 'epoch': 7.96}
{'loss': 0.045, 'grad_norm': 0.16907541453838348, 'learning_rate': 1.9642857142857145e-05, 'epoch': 8.02}
{'loss': 0.0376, 'grad_norm': 0.3272036910057068, 'learning_rate': 1.8956043956043957e-05, 'epoch': 8.09}
{'loss': 0.0387, 'grad_norm': 0.32160505652427673, 'learning_rate': 1.826923076923077e-05, 'epoch': 8.16}
{'loss': 0.0428, 'grad_norm': 0.31524384021759033, 'learning_rate': 1.7582417582417584e-05, 'epoch': 8.23}
{'loss': 0.0374, 'grad_norm': 0.32698854804039, 'learning_rate': 1.68956043956044e-05, 'epoch': 8.3}
{'loss': 0.0401, 'grad_norm': 0.2444036453962326, 'learning_rate': 1.620879120879121e-05, 'epoch': 8.37}
{'loss': 0.0399, 'grad_norm': 0.29891079664230347, 'learning_rate': 1.5521978021978023e-05, 'epoch': 8.44}
{'loss': 0.0464, 'grad_norm': 0.33276841044425964, 'learning_rate': 1.4835164835164836e-05, 'epoch': 8.5}
{'loss': 0.0412, 'grad_norm': 0.11906079947948456, 'learning_rate': 1.4148351648351648e-05, 'epoch': 8.57}
{'loss': 0.0351, 'grad_norm': 0.2907005250453949, 'learning_rate': 1.3461538461538462e-05, 'epoch': 8.64}
{'loss': 0.0359, 'grad_norm': 0.17415064573287964, 'learning_rate': 1.2774725274725275e-05, 'epoch': 8.71}
{'loss': 0.041, 'grad_norm': 0.28198644518852234, 'learning_rate': 1.2087912087912089e-05, 'epoch': 8.78}
{'loss': 0.0413, 'grad_norm': 0.33856770396232605, 'learning_rate': 1.14010989010989e-05, 'epoch': 8.85}
{'loss': 0.0369, 'grad_norm': 0.30443742871284485, 'learning_rate': 1.0714285714285714e-05, 'epoch': 8.92}
{'loss': 0.0376, 'grad_norm': 0.23951974511146545, 'learning_rate': 1.0027472527472528e-05, 'epoch': 8.98}
{'loss': 0.0328, 'grad_norm': 0.14999833703041077, 'learning_rate': 9.340659340659341e-06, 'epoch': 9.05}
{'loss': 0.0337, 'grad_norm': 0.5815431475639343, 'learning_rate': 8.653846153846155e-06, 'epoch': 9.12}
{'loss': 0.0343, 'grad_norm': 0.162886381149292, 'learning_rate': 7.967032967032966e-06, 'epoch': 9.19}
{'loss': 0.0342, 'grad_norm': 0.3085672855377197, 'learning_rate': 7.280219780219781e-06, 'epoch': 9.26}
{'loss': 0.0313, 'grad_norm': 0.19501757621765137, 'learning_rate': 6.5934065934065935e-06, 'epoch': 9.33}
{'loss': 0.0346, 'grad_norm': 0.4212968349456787, 'learning_rate': 5.906593406593407e-06, 'epoch': 9.4}
{'loss': 0.0342, 'grad_norm': 0.11318781226873398, 'learning_rate': 5.219780219780221e-06, 'epoch': 9.46}
{'loss': 0.0324, 'grad_norm': 0.34787067770957947, 'learning_rate': 4.532967032967033e-06, 'epoch': 9.53}
{'loss': 0.0334, 'grad_norm': 0.22008398175239563, 'learning_rate': 3.846153846153847e-06, 'epoch': 9.6}
{'loss': 0.0322, 'grad_norm': 0.1361882984638214, 'learning_rate': 3.1593406593406595e-06, 'epoch': 9.67}
{'loss': 0.0341, 'grad_norm': 0.2948777377605438, 'learning_rate': 2.4725274725274726e-06, 'epoch': 9.74}
{'loss': 0.0327, 'grad_norm': 0.1033204197883606, 'learning_rate': 1.7857142857142857e-06, 'epoch': 9.81}
{'loss': 0.0336, 'grad_norm': 0.14806444942951202, 'learning_rate': 1.098901098901099e-06, 'epoch': 9.88}
{'loss': 0.0299, 'grad_norm': 0.12937778234481812, 'learning_rate': 4.120879120879121e-07, 'epoch': 9.94}
[2025-07-07 17:35:04,869] [INFO] [logging.py:128:log_dist] [Rank 0] [Torch] Checkpoint global_step3640 is about to be saved!
[2025-07-07 17:35:08,453] [INFO] [logging.py:128:log_dist] [Rank 0] Saving model checkpoint: data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/global_step3640/mp_rank_00_model_states.pt
[2025-07-07 17:35:08,460] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/global_step3640/mp_rank_00_model_states.pt...
[2025-07-07 17:36:10,514] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/global_step3640/mp_rank_00_model_states.pt.
[2025-07-07 17:36:10,555] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/global_step3640/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt...
[2025-07-07 17:36:23,155] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/global_step3640/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt.
[2025-07-07 17:36:23,210] [INFO] [engine.py:3536:_save_zero_checkpoint] zero checkpoint saved data/output/mistral_lora_moe_cybersecurity_top1/checkpoint-3640/global_step3640/bf16_zero_pp_rank_0_mp_rank_00_optim_states.pt
[2025-07-07 17:36:23,215] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step3640 is ready now!
{'train_runtime': 5133.2839, 'train_samples_per_second': 11.365, 'train_steps_per_second': 0.709, 'train_loss': 0.28571803716349076, 'epoch': 9.99}
Training completed!
Running final evaluation...
Evaluation metrics at step 3640: {'eval_loss': 0.04417138174176216, 'eval_runtime': 5.992, 'eval_samples_per_second': 32.377, 'eval_steps_per_second': 8.178}Evaluation metrics at step 3640: {'eval_loss': 0.04417138174176216, 'eval_runtime': 5.9919, 'eval_samples_per_second': 32.377, 'eval_steps_per_second': 8.178}Evaluation metrics at step 3640: {'eval_loss': 0.04417138174176216, 'eval_runtime': 5.9913, 'eval_samples_per_second': 32.38, 'eval_steps_per_second': 8.179}Evaluation metrics at step 3640: {'eval_loss': 0.04417138174176216, 'eval_runtime': 5.914, 'eval_samples_per_second': 32.804, 'eval_steps_per_second': 8.285}



Final evaluation results: {'eval_loss': 0.04417138174176216, 'eval_runtime': 5.914, 'eval_samples_per_second': 32.804, 'eval_steps_per_second': 8.285, 'epoch': 9.985606579849211}
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855409:2856180 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855409:2875475 [2] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855408:2856178 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g020:2855409:2856180 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855409:2856180 [2] NCCL INFO misc/socket.cc:881 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855408:2856178 [1] NCCL INFO misc/socket.cc:881 -> 3
cn-g020:2855410:2856182 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:64 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:80 -> 3
cn-g020:2855408:2875477 [1] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855410:2875479 [3] NCCL INFO misc/socket.cc:829 -> 3
cn-g020:2855410:2856182 [3] NCCL INFO misc/socket.cc:881 -> 3
cn-g020:2855407:2856280 [0] NCCL INFO [Service thread] Connection closed by localRank 1
cn-g020:2855407:2856280 [0] NCCL INFO [Service thread] Connection closed by localRank 2
cn-g020:2855407:2856280 [0] NCCL INFO [Service thread] Connection closed by localRank 3
cn-g020:2855407:2875473 [0] NCCL INFO comm 0x5556fd7d8480 rank 0 nranks 4 cudaDev 0 busId 1000 - Destroy COMPLETE
Job complete

======== GPU REPORT ========

==============NVSMI LOG==============

Timestamp                                 : Mon Jul  7 17:52:46 2025
Driver Version                            : 535.247.01
CUDA Version                              : 12.2

Attached GPUs                             : 4
GPU ********:01:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2855409
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 6244846 ms
            Is Running                    : 0
        Process ID                        : 2855408
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 6245102 ms
            Is Running                    : 0
        Process ID                        : 2855410
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3936 MiB
            Time                          : 6245359 ms
            Is Running                    : 0
        Process ID                        : 2855407
            GPU Utilization               : 65 %
            Memory Utilization            : 23 %
            Max memory usage              : 39748 MiB
            Time                          : 6245267 ms
            Is Running                    : 0

GPU ********:41:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2855409
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6244845 ms
            Is Running                    : 0
        Process ID                        : 2855408
            GPU Utilization               : 68 %
            Memory Utilization            : 23 %
            Max memory usage              : 40350 MiB
            Time                          : 6245100 ms
            Is Running                    : 0
        Process ID                        : 2855410
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6245355 ms
            Is Running                    : 0
        Process ID                        : 2855407
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6245265 ms
            Is Running                    : 0

GPU ********:81:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2855409
            GPU Utilization               : 66 %
            Memory Utilization            : 23 %
            Max memory usage              : 41858 MiB
            Time                          : 6244844 ms
            Is Running                    : 0
        Process ID                        : 2855408
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6245098 ms
            Is Running                    : 0
        Process ID                        : 2855410
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6245353 ms
            Is Running                    : 0
        Process ID                        : 2855407
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 4256 MiB
            Time                          : 6245264 ms
            Is Running                    : 0

GPU ********:C1:00.0
    Accounting Mode                       : Enabled
    Accounting Mode Buffer Size           : 4000
    Accounted Processes
        Process ID                        : 2855409
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 6244842 ms
            Is Running                    : 0
        Process ID                        : 2855408
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 6245097 ms
            Is Running                    : 0
        Process ID                        : 2855410
            GPU Utilization               : 66 %
            Memory Utilization            : 23 %
            Max memory usage              : 38742 MiB
            Time                          : 6245351 ms
            Is Running                    : 0
        Process ID                        : 2855407
            GPU Utilization               : 0 %
            Memory Utilization            : 0 %
            Max memory usage              : 3652 MiB
            Time                          : 6245263 ms
            Is Running                    : 0

Mon Jul  7 17:52:59 2025       
+---------------------------------------------------------------------------------------+
| NVIDIA-SMI 535.247.01             Driver Version: 535.247.01   CUDA Version: 12.2     |
|-----------------------------------------+----------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id        Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |         Memory-Usage | GPU-Util  Compute M. |
|                                         |                      |               MIG M. |
|=========================================+======================+======================|
|   0  NVIDIA A100-SXM4-80GB          On  | ********:01:00.0 Off |                    0 |
| N/A   28C    P0              62W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   1  NVIDIA A100-SXM4-80GB          On  | ********:41:00.0 Off |                    0 |
| N/A   25C    P0              62W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   2  NVIDIA A100-SXM4-80GB          On  | ********:81:00.0 Off |                    0 |
| N/A   29C    P0              65W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
|   3  NVIDIA A100-SXM4-80GB          On  | ********:C1:00.0 Off |                    0 |
| N/A   26C    P0              61W / 500W |      0MiB / 81920MiB |      0%      Default |
|                                         |                      |             Disabled |
+-----------------------------------------+----------------------+----------------------+
                                                                                         
+---------------------------------------------------------------------------------------+
| Processes:                                                                            |
|  GPU   GI   CI        PID   Type   Process name                            GPU Memory |
|        ID   ID                                                             Usage      |
|=======================================================================================|
|  No running processes found                                                           |
+---------------------------------------------------------------------------------------+
