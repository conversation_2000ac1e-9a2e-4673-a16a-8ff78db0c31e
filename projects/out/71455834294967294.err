[=== Module python/3.10 loaded ===]
[=== Module cudatoolkit/12.1.1 loaded ===]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] 
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] *****************************************
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] Setting OMP_NUM_THREADS environment variable for each process to be 1 in default, to avoid your system being overloaded, please further tune the variable for optimal performance in your application as needed. 
W0707 18:20:50.163000 3813190 torch/distributed/run.py:766] *****************************************
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/utils/hub.py:128: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.
  warnings.warn(
wandb: Currently logged in as: maryamha (sarath-chandar) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.20.1
wandb: Run data is saved locally in /network/scratch/m/maryam.hashemzadeh/saftly/mergoo/projects/wandb/run-20250707_182101-2wjetsf6
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run mistral-economic-moe
wandb: ⭐️ View project at https://wandb.ai/sarath-chandar/mistral-moe-training
wandb: 🚀 View run at https://wandb.ai/sarath-chandar/mistral-moe-training/runs/2wjetsf6

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.24s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.65s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.97s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:13<00:13, 13.97s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  8.97s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.73s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.27s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.01s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.14s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.87s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.71s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:19<00:00,  9.88s/it]



  0%|          | 0/675 [00:00<?, ?it/s]
100%|██████████| 675/675 [00:00<00:00, 99541.35it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]
  0%|          | 0/675 [00:00<?, ?it/s]

100%|██████████| 675/675 [00:00<00:00, 44349.75it/s]
100%|██████████| 675/675 [00:00<00:00, 47166.27it/s]
100%|██████████| 675/675 [00:00<00:00, 50077.92it/s]


Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
Note that `shard_checkpoint` is deprecated and will be removed in v4.44. We recommend you using split_torch_state_dict_into_shards from huggingface_hub library
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]MistralForCausalLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.

Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:   0%|          | 0/2 [00:00<?, ?it/s]
Loading checkpoint shards:  50%|█████     | 1/2 [00:08<00:08,  8.43s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:09<00:09,  9.25s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:10<00:10, 10.10s/it]
Loading checkpoint shards:  50%|█████     | 1/2 [00:08<00:08,  8.65s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.11s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.32s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Generating train split: 0 examples [00:00, ? examples/s]
Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.31s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  7.66s/it]
Loading checkpoint shards: 100%|██████████| 2/2 [00:15<00:00,  7.60s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.06s/it]Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Loading checkpoint shards: 100%|██████████| 2/2 [00:16<00:00,  8.03s/it]

Loading checkpoint shards: 100%|██████████| 2/2 [00:14<00:00,  7.31s/it]
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.
Some weights of MistralForCausalLM were not initialized from the model checkpoint at data/mistral_lora_moe_economic_top1 and are newly initialized: ['model.layers.0.mlp.down_proj.gate.weight', 'model.layers.0.mlp.gate_proj.gate.weight', 'model.layers.0.mlp.up_proj.gate.weight', 'model.layers.1.mlp.down_proj.gate.weight', 'model.layers.1.mlp.gate_proj.gate.weight', 'model.layers.1.mlp.up_proj.gate.weight', 'model.layers.10.mlp.down_proj.gate.weight', 'model.layers.10.mlp.gate_proj.gate.weight', 'model.layers.10.mlp.up_proj.gate.weight', 'model.layers.11.mlp.down_proj.gate.weight', 'model.layers.11.mlp.gate_proj.gate.weight', 'model.layers.11.mlp.up_proj.gate.weight', 'model.layers.12.mlp.down_proj.gate.weight', 'model.layers.12.mlp.gate_proj.gate.weight', 'model.layers.12.mlp.up_proj.gate.weight', 'model.layers.13.mlp.down_proj.gate.weight', 'model.layers.13.mlp.gate_proj.gate.weight', 'model.layers.13.mlp.up_proj.gate.weight', 'model.layers.14.mlp.down_proj.gate.weight', 'model.layers.14.mlp.gate_proj.gate.weight', 'model.layers.14.mlp.up_proj.gate.weight', 'model.layers.15.mlp.down_proj.gate.weight', 'model.layers.15.mlp.gate_proj.gate.weight', 'model.layers.15.mlp.up_proj.gate.weight', 'model.layers.16.mlp.down_proj.gate.weight', 'model.layers.16.mlp.gate_proj.gate.weight', 'model.layers.16.mlp.up_proj.gate.weight', 'model.layers.17.mlp.down_proj.gate.weight', 'model.layers.17.mlp.gate_proj.gate.weight', 'model.layers.17.mlp.up_proj.gate.weight', 'model.layers.18.mlp.down_proj.gate.weight', 'model.layers.18.mlp.gate_proj.gate.weight', 'model.layers.18.mlp.up_proj.gate.weight', 'model.layers.19.mlp.down_proj.gate.weight', 'model.layers.19.mlp.gate_proj.gate.weight', 'model.layers.19.mlp.up_proj.gate.weight', 'model.layers.2.mlp.down_proj.gate.weight', 'model.layers.2.mlp.gate_proj.gate.weight', 'model.layers.2.mlp.up_proj.gate.weight', 'model.layers.20.mlp.down_proj.gate.weight', 'model.layers.20.mlp.gate_proj.gate.weight', 'model.layers.20.mlp.up_proj.gate.weight', 'model.layers.21.mlp.down_proj.gate.weight', 'model.layers.21.mlp.gate_proj.gate.weight', 'model.layers.21.mlp.up_proj.gate.weight', 'model.layers.22.mlp.down_proj.gate.weight', 'model.layers.22.mlp.gate_proj.gate.weight', 'model.layers.22.mlp.up_proj.gate.weight', 'model.layers.23.mlp.down_proj.gate.weight', 'model.layers.23.mlp.gate_proj.gate.weight', 'model.layers.23.mlp.up_proj.gate.weight', 'model.layers.24.mlp.down_proj.gate.weight', 'model.layers.24.mlp.gate_proj.gate.weight', 'model.layers.24.mlp.up_proj.gate.weight', 'model.layers.25.mlp.down_proj.gate.weight', 'model.layers.25.mlp.gate_proj.gate.weight', 'model.layers.25.mlp.up_proj.gate.weight', 'model.layers.26.mlp.down_proj.gate.weight', 'model.layers.26.mlp.gate_proj.gate.weight', 'model.layers.26.mlp.up_proj.gate.weight', 'model.layers.27.mlp.down_proj.gate.weight', 'model.layers.27.mlp.gate_proj.gate.weight', 'model.layers.27.mlp.up_proj.gate.weight', 'model.layers.28.mlp.down_proj.gate.weight', 'model.layers.28.mlp.gate_proj.gate.weight', 'model.layers.28.mlp.up_proj.gate.weight', 'model.layers.29.mlp.down_proj.gate.weight', 'model.layers.29.mlp.gate_proj.gate.weight', 'model.layers.29.mlp.up_proj.gate.weight', 'model.layers.3.mlp.down_proj.gate.weight', 'model.layers.3.mlp.gate_proj.gate.weight', 'model.layers.3.mlp.up_proj.gate.weight', 'model.layers.30.mlp.down_proj.gate.weight', 'model.layers.30.mlp.gate_proj.gate.weight', 'model.layers.30.mlp.up_proj.gate.weight', 'model.layers.31.mlp.down_proj.gate.weight', 'model.layers.31.mlp.gate_proj.gate.weight', 'model.layers.31.mlp.up_proj.gate.weight', 'model.layers.4.mlp.down_proj.gate.weight', 'model.layers.4.mlp.gate_proj.gate.weight', 'model.layers.4.mlp.up_proj.gate.weight', 'model.layers.5.mlp.down_proj.gate.weight', 'model.layers.5.mlp.gate_proj.gate.weight', 'model.layers.5.mlp.up_proj.gate.weight', 'model.layers.6.mlp.down_proj.gate.weight', 'model.layers.6.mlp.gate_proj.gate.weight', 'model.layers.6.mlp.up_proj.gate.weight', 'model.layers.7.mlp.down_proj.gate.weight', 'model.layers.7.mlp.gate_proj.gate.weight', 'model.layers.7.mlp.up_proj.gate.weight', 'model.layers.8.mlp.down_proj.gate.weight', 'model.layers.8.mlp.gate_proj.gate.weight', 'model.layers.8.mlp.up_proj.gate.weight', 'model.layers.9.mlp.down_proj.gate.weight', 'model.layers.9.mlp.gate_proj.gate.weight', 'model.layers.9.mlp.up_proj.gate.weight']
You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.

Generating train split: 4104 examples [00:00, 26233.70 examples/s]
Generating train split: 4104 examples [00:00, 24608.96 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/huggingface_hub/utils/_deprecation.py:100: FutureWarning: Deprecated argument(s) used in '__init__': max_seq_length. Will not be supported from version '0.13.0'.

Deprecated positional argument(s) used in SFTTrainer, please use the SFTConfig to set these arguments instead.
  warnings.warn(message, FutureWarning)
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/transformers/training_args.py:1568: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead
  warnings.warn(
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
Using `include_inputs_for_metrics` is deprecated and will be removed in version 5 of 🤗 Transformers. Please use `include_for_metrics` list argument instead.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank2]:[W707 18:22:47.601518094 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 2]  using GPU 2 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
[rank3]:[W707 18:22:47.603571826 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 3]  using GPU 3 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(

Map:   0%|          | 0/2000 [00:00<?, ? examples/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:300: UserWarning: You passed a `max_seq_length` argument to the SFTTrainer, the value you passed will override the one in the `SFTConfig`.
  warnings.warn(
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank1]:[W707 18:22:47.658836892 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.

Map:  50%|█████     | 1000/2000 [00:00<00:00, 4666.45 examples/s]
Map: 100%|██████████| 2000/2000 [00:00<00:00, 5066.72 examples/s]
Map: 100%|██████████| 2000/2000 [00:00<00:00, 4788.60 examples/s]

Map:   0%|          | 0/123 [00:00<?, ? examples/s]
Map: 100%|██████████| 123/123 [00:00<00:00, 3418.14 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user. 
  warnings.warn(  # warn only once
[rank0]:[W707 18:22:47.142133361 ProcessGroupNCCL.cpp:4718] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 as device used by this process is currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect. You can pecify device_id in init_process_group() to force use of a particular device.
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/2000 [00:00<?, ? examples/s]
Map:   0%|          | 0/2000 [00:00<?, ? examples/s]
Map:   0%|          | 0/2000 [00:00<?, ? examples/s]Using auto half precision backend

Map:  50%|█████     | 1000/2000 [00:00<00:00, 1828.44 examples/s]
Map:  50%|█████     | 1000/2000 [00:00<00:00, 1702.25 examples/s]
Map:  50%|█████     | 1000/2000 [00:00<00:00, 1697.93 examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1952.30 examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1895.75 examples/s]

Map: 100%|██████████| 2000/2000 [00:01<00:00, 1891.87 examples/s]
Map:   0%|          | 0/123 [00:00<?, ? examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1851.28 examples/s]
Map: 100%|██████████| 2000/2000 [00:01<00:00, 1829.78 examples/s]

Map: 100%|██████████| 2000/2000 [00:01<00:00, 1790.06 examples/s]

Map: 100%|██████████| 123/123 [00:00<00:00, 3001.44 examples/s]

Map:   0%|          | 0/123 [00:00<?, ? examples/s]/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map:   0%|          | 0/123 [00:00<?, ? examples/s]
Map: 100%|██████████| 123/123 [00:00<00:00, 2135.57 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(

Map: 100%|██████████| 123/123 [00:00<00:00, 2237.40 examples/s]
/home/<USER>/m/maryam.hashemzadeh/projects/envs/mergoo_2/lib/python3.10/site-packages/trl/trainer/sft_trainer.py:403: UserWarning: You passed a processing_class with `padding_side` not equal to `right` to the SFTTrainer. This might lead to some unexpected behaviour due to overflow issues when training a model in half-precision. You might consider adding `processing_class.padding_side = 'right'` to your code.
  warnings.warn(
***** Running training *****
  Num examples = 2,000
  Num Epochs = 10
  Instantaneous batch size per device = 1
  Total train batch size (w. parallel, distributed & accumulation) = 16
  Gradient Accumulation steps = 4
  Total optimization steps = 1,250
  Number of trainable parameters = 1,918,238,720
Automatic Weights & Biases logging enabled, to disable set os.environ["WANDB_DISABLED"] = "true"

  0%|          | 0/1250 [00:00<?, ?it/s]
  0%|          | 1/1250 [00:02<43:02,  2.07s/it]
                                                

  0%|          | 1/1250 [00:02<43:02,  2.07s/it]
  0%|          | 2/1250 [00:03<35:19,  1.70s/it]
  0%|          | 3/1250 [00:04<32:30,  1.56s/it]
  0%|          | 4/1250 [00:06<31:11,  1.50s/it]
  0%|          | 5/1250 [00:07<30:31,  1.47s/it]
  0%|          | 6/1250 [00:09<30:02,  1.45s/it]
  1%|          | 7/1250 [00:10<29:42,  1.43s/it]
  1%|          | 8/1250 [00:11<29:30,  1.43s/it]
  1%|          | 9/1250 [00:13<29:23,  1.42s/it]
  1%|          | 10/1250 [00:14<29:17,  1.42s/it]
  1%|          | 11/1250 [00:16<29:14,  1.42s/it]
  1%|          | 12/1250 [00:17<29:10,  1.41s/it]
  1%|          | 13/1250 [00:19<29:08,  1.41s/it]
  1%|          | 14/1250 [00:20<29:02,  1.41s/it]
  1%|          | 15/1250 [00:21<29:00,  1.41s/it]
  1%|▏         | 16/1250 [00:23<28:55,  1.41s/it]
  1%|▏         | 17/1250 [00:24<28:48,  1.40s/it]
  1%|▏         | 18/1250 [00:26<28:50,  1.40s/it]
  2%|▏         | 19/1250 [00:27<28:44,  1.40s/it]
  2%|▏         | 20/1250 [00:28<28:47,  1.40s/it]
  2%|▏         | 21/1250 [00:30<28:45,  1.40s/it]
  2%|▏         | 22/1250 [00:31<28:42,  1.40s/it]
  2%|▏         | 23/1250 [00:33<28:45,  1.41s/it]
  2%|▏         | 24/1250 [00:34<28:46,  1.41s/it]
  2%|▏         | 25/1250 [00:35<28:44,  1.41s/it]
                                                 

  2%|▏         | 25/1250 [00:35<28:44,  1.41s/it]
  2%|▏         | 26/1250 [00:37<28:47,  1.41s/it]
  2%|▏         | 27/1250 [00:38<28:44,  1.41s/it]
  2%|▏         | 28/1250 [00:40<28:41,  1.41s/it]
  2%|▏         | 29/1250 [00:41<28:36,  1.41s/it]
  2%|▏         | 30/1250 [00:42<28:38,  1.41s/it]
  2%|▏         | 31/1250 [00:44<28:36,  1.41s/it]
  3%|▎         | 32/1250 [00:45<28:33,  1.41s/it]
  3%|▎         | 33/1250 [00:47<28:34,  1.41s/it]
  3%|▎         | 34/1250 [00:48<28:34,  1.41s/it]
  3%|▎         | 35/1250 [00:49<28:31,  1.41s/it]
  3%|▎         | 36/1250 [00:51<28:26,  1.41s/it]
  3%|▎         | 37/1250 [00:52<28:26,  1.41s/it]
  3%|▎         | 38/1250 [00:54<28:19,  1.40s/it]
  3%|▎         | 39/1250 [00:55<28:17,  1.40s/it]
  3%|▎         | 40/1250 [00:56<28:16,  1.40s/it]
  3%|▎         | 41/1250 [00:58<28:14,  1.40s/it]
  3%|▎         | 42/1250 [00:59<28:13,  1.40s/it]
  3%|▎         | 43/1250 [01:01<28:12,  1.40s/it]
  4%|▎         | 44/1250 [01:02<28:14,  1.40s/it]
  4%|▎         | 45/1250 [01:03<28:16,  1.41s/it]
  4%|▎         | 46/1250 [01:05<28:10,  1.40s/it]
  4%|▍         | 47/1250 [01:06<28:10,  1.41s/it]
  4%|▍         | 48/1250 [01:08<28:09,  1.41s/it]
  4%|▍         | 49/1250 [01:09<28:09,  1.41s/it]
  4%|▍         | 50/1250 [01:11<28:07,  1.41s/it]
                                                 

  4%|▍         | 50/1250 [01:11<28:07,  1.41s/it]
  4%|▍         | 51/1250 [01:12<28:08,  1.41s/it]
  4%|▍         | 52/1250 [01:13<28:04,  1.41s/it]
  4%|▍         | 53/1250 [01:15<28:02,  1.41s/it]
  4%|▍         | 54/1250 [01:16<28:02,  1.41s/it]
  4%|▍         | 55/1250 [01:18<27:58,  1.40s/it]
  4%|▍         | 56/1250 [01:19<27:57,  1.40s/it]
  5%|▍         | 57/1250 [01:20<27:47,  1.40s/it]
  5%|▍         | 58/1250 [01:22<27:42,  1.39s/it]
  5%|▍         | 59/1250 [01:23<27:46,  1.40s/it]
  5%|▍         | 60/1250 [01:25<27:47,  1.40s/it]
  5%|▍         | 61/1250 [01:26<27:48,  1.40s/it]
  5%|▍         | 62/1250 [01:27<27:46,  1.40s/it]
  5%|▌         | 63/1250 [01:29<27:45,  1.40s/it]
  5%|▌         | 64/1250 [01:30<27:45,  1.40s/it]
  5%|▌         | 65/1250 [01:32<27:46,  1.41s/it]
  5%|▌         | 66/1250 [01:33<27:42,  1.40s/it]
  5%|▌         | 67/1250 [01:34<27:40,  1.40s/it]
  5%|▌         | 68/1250 [01:36<27:42,  1.41s/it]
  6%|▌         | 69/1250 [01:37<27:43,  1.41s/it]
  6%|▌         | 70/1250 [01:39<27:41,  1.41s/it]
  6%|▌         | 71/1250 [01:40<27:39,  1.41s/it]
  6%|▌         | 72/1250 [01:41<27:35,  1.41s/it]
  6%|▌         | 73/1250 [01:43<27:36,  1.41s/it]
  6%|▌         | 74/1250 [01:44<28:47,  1.47s/it]
  6%|▌         | 75/1250 [01:46<28:24,  1.45s/it]
                                                 

  6%|▌         | 75/1250 [01:46<28:24,  1.45s/it]
  6%|▌         | 76/1250 [01:47<28:10,  1.44s/it]
  6%|▌         | 77/1250 [01:49<27:57,  1.43s/it]
  6%|▌         | 78/1250 [01:50<27:44,  1.42s/it]
  6%|▋         | 79/1250 [01:51<27:37,  1.42s/it]
  6%|▋         | 80/1250 [01:53<27:36,  1.42s/it]
  6%|▋         | 81/1250 [01:54<27:32,  1.41s/it]
  7%|▋         | 82/1250 [01:56<27:30,  1.41s/it]
  7%|▋         | 83/1250 [01:57<27:25,  1.41s/it]
  7%|▋         | 84/1250 [01:59<27:27,  1.41s/it]
  7%|▋         | 85/1250 [02:00<27:21,  1.41s/it]
  7%|▋         | 86/1250 [02:01<27:19,  1.41s/it]
  7%|▋         | 87/1250 [02:03<27:19,  1.41s/it]
  7%|▋         | 88/1250 [02:04<27:15,  1.41s/it]
  7%|▋         | 89/1250 [02:06<27:12,  1.41s/it]
  7%|▋         | 90/1250 [02:07<27:08,  1.40s/it]
  7%|▋         | 91/1250 [02:08<27:10,  1.41s/it]
  7%|▋         | 92/1250 [02:10<27:07,  1.41s/it]
  7%|▋         | 93/1250 [02:11<27:05,  1.41s/it]
  8%|▊         | 94/1250 [02:13<27:08,  1.41s/it]
  8%|▊         | 95/1250 [02:14<27:06,  1.41s/it]
  8%|▊         | 96/1250 [02:15<27:04,  1.41s/it]
  8%|▊         | 97/1250 [02:17<27:01,  1.41s/it]
  8%|▊         | 98/1250 [02:18<27:02,  1.41s/it]
  8%|▊         | 99/1250 [02:20<26:59,  1.41s/it]
  8%|▊         | 100/1250 [02:21<26:57,  1.41s/it]
                                                  

  8%|▊         | 100/1250 [02:21<26:57,  1.41s/it]
  8%|▊         | 101/1250 [02:22<26:58,  1.41s/it]
  8%|▊         | 102/1250 [02:24<26:54,  1.41s/it]
  8%|▊         | 103/1250 [02:25<26:47,  1.40s/it]
  8%|▊         | 104/1250 [02:27<26:48,  1.40s/it]
  8%|▊         | 105/1250 [02:28<26:47,  1.40s/it]
  8%|▊         | 106/1250 [02:29<26:46,  1.40s/it]
  9%|▊         | 107/1250 [02:31<26:46,  1.41s/it]
  9%|▊         | 108/1250 [02:32<26:38,  1.40s/it]
  9%|▊         | 109/1250 [02:34<26:41,  1.40s/it]
  9%|▉         | 110/1250 [02:35<26:43,  1.41s/it]
  9%|▉         | 111/1250 [02:36<26:42,  1.41s/it]
  9%|▉         | 112/1250 [02:38<26:33,  1.40s/it]
  9%|▉         | 113/1250 [02:39<26:33,  1.40s/it]
  9%|▉         | 114/1250 [02:41<26:32,  1.40s/it]
  9%|▉         | 115/1250 [02:42<26:33,  1.40s/it]
  9%|▉         | 116/1250 [02:43<26:33,  1.41s/it]
  9%|▉         | 117/1250 [02:45<26:30,  1.40s/it]
  9%|▉         | 118/1250 [02:46<26:30,  1.41s/it]
 10%|▉         | 119/1250 [02:48<26:29,  1.41s/it]
 10%|▉         | 120/1250 [02:49<26:29,  1.41s/it]
 10%|▉         | 121/1250 [02:51<26:26,  1.41s/it]
 10%|▉         | 122/1250 [02:52<26:24,  1.41s/it]
 10%|▉         | 123/1250 [02:53<26:26,  1.41s/it]
 10%|▉         | 124/1250 [02:55<26:22,  1.41s/it]
 10%|█         | 125/1250 [02:56<26:17,  1.40s/it]
                                                  

 10%|█         | 125/1250 [02:56<26:17,  1.40s/it]
 10%|█         | 126/1250 [02:58<26:24,  1.41s/it]
 10%|█         | 127/1250 [02:59<26:22,  1.41s/it]
 10%|█         | 128/1250 [03:00<26:19,  1.41s/it]
 10%|█         | 129/1250 [03:02<26:15,  1.41s/it]
 10%|█         | 130/1250 [03:03<26:19,  1.41s/it]
 10%|█         | 131/1250 [03:05<26:16,  1.41s/it]
 11%|█         | 132/1250 [03:06<26:11,  1.41s/it]
 11%|█         | 133/1250 [03:07<26:10,  1.41s/it]
 11%|█         | 134/1250 [03:09<26:09,  1.41s/it]
 11%|█         | 135/1250 [03:10<26:07,  1.41s/it]
 11%|█         | 136/1250 [03:12<26:04,  1.40s/it]
 11%|█         | 137/1250 [03:13<26:03,  1.41s/it]
 11%|█         | 138/1250 [03:14<26:02,  1.41s/it]
 11%|█         | 139/1250 [03:16<25:55,  1.40s/it]
 11%|█         | 140/1250 [03:17<25:53,  1.40s/it]
 11%|█▏        | 141/1250 [03:19<25:55,  1.40s/it]
 11%|█▏        | 142/1250 [03:20<25:54,  1.40s/it]
 11%|█▏        | 143/1250 [03:21<25:50,  1.40s/it]
 12%|█▏        | 144/1250 [03:23<25:45,  1.40s/it]
 12%|█▏        | 145/1250 [03:24<25:45,  1.40s/it]
 12%|█▏        | 146/1250 [03:26<25:48,  1.40s/it]
 12%|█▏        | 147/1250 [03:27<25:46,  1.40s/it]
 12%|█▏        | 148/1250 [03:28<25:47,  1.40s/it]
 12%|█▏        | 149/1250 [03:30<25:44,  1.40s/it]
 12%|█▏        | 150/1250 [03:31<25:43,  1.40s/it]
                                                  

 12%|█▏        | 150/1250 [03:31<25:43,  1.40s/it]
 12%|█▏        | 151/1250 [03:33<25:53,  1.41s/it]
 12%|█▏        | 152/1250 [03:34<25:49,  1.41s/it]
 12%|█▏        | 153/1250 [03:35<25:46,  1.41s/it]
 12%|█▏        | 154/1250 [03:37<25:43,  1.41s/it]
 12%|█▏        | 155/1250 [03:38<25:38,  1.41s/it]
 12%|█▏        | 156/1250 [03:40<25:37,  1.41s/it]
 13%|█▎        | 157/1250 [03:41<25:36,  1.41s/it]
 13%|█▎        | 158/1250 [03:43<25:39,  1.41s/it]
 13%|█▎        | 159/1250 [03:44<25:35,  1.41s/it]
 13%|█▎        | 160/1250 [03:45<25:31,  1.40s/it]
 13%|█▎        | 161/1250 [03:47<25:28,  1.40s/it]
 13%|█▎        | 162/1250 [03:48<25:27,  1.40s/it]
 13%|█▎        | 163/1250 [03:50<25:26,  1.40s/it]
 13%|█▎        | 164/1250 [03:51<25:19,  1.40s/it]
 13%|█▎        | 165/1250 [03:52<25:20,  1.40s/it]
 13%|█▎        | 166/1250 [03:54<25:19,  1.40s/it]
 13%|█▎        | 167/1250 [03:55<25:16,  1.40s/it]
 13%|█▎        | 168/1250 [03:57<25:16,  1.40s/it]
 14%|█▎        | 169/1250 [03:58<25:17,  1.40s/it]
 14%|█▎        | 170/1250 [03:59<25:18,  1.41s/it]
 14%|█▎        | 171/1250 [04:01<25:15,  1.40s/it]
 14%|█▍        | 172/1250 [04:02<25:15,  1.41s/it]
 14%|█▍        | 173/1250 [04:04<25:15,  1.41s/it]
 14%|█▍        | 174/1250 [04:05<25:13,  1.41s/it]
 14%|█▍        | 175/1250 [04:06<25:13,  1.41s/it]
                                                  

 14%|█▍        | 175/1250 [04:06<25:13,  1.41s/it]
 14%|█▍        | 176/1250 [04:08<25:17,  1.41s/it]
 14%|█▍        | 177/1250 [04:09<25:13,  1.41s/it]
 14%|█▍        | 178/1250 [04:11<25:09,  1.41s/it]
 14%|█▍        | 179/1250 [04:12<25:06,  1.41s/it]
 14%|█▍        | 180/1250 [04:13<25:05,  1.41s/it]
 14%|█▍        | 181/1250 [04:15<25:04,  1.41s/it]
 15%|█▍        | 182/1250 [04:16<25:02,  1.41s/it]
 15%|█▍        | 183/1250 [04:18<25:03,  1.41s/it]
 15%|█▍        | 184/1250 [04:19<24:53,  1.40s/it]
 15%|█▍        | 185/1250 [04:20<24:52,  1.40s/it]
 15%|█▍        | 186/1250 [04:22<24:49,  1.40s/it]
 15%|█▍        | 187/1250 [04:23<25:54,  1.46s/it]
 15%|█▌        | 188/1250 [04:25<25:34,  1.44s/it]
 15%|█▌        | 189/1250 [04:26<25:16,  1.43s/it]
 15%|█▌        | 190/1250 [04:28<25:09,  1.42s/it]
 15%|█▌        | 191/1250 [04:29<25:03,  1.42s/it]
 15%|█▌        | 192/1250 [04:30<24:59,  1.42s/it]
 15%|█▌        | 193/1250 [04:32<24:51,  1.41s/it]
 16%|█▌        | 194/1250 [04:33<24:49,  1.41s/it]
 16%|█▌        | 195/1250 [04:35<24:47,  1.41s/it]
 16%|█▌        | 196/1250 [04:36<24:42,  1.41s/it]
 16%|█▌        | 197/1250 [04:37<24:41,  1.41s/it]
 16%|█▌        | 198/1250 [04:39<24:33,  1.40s/it]
 16%|█▌        | 199/1250 [04:40<24:25,  1.39s/it]
 16%|█▌        | 200/1250 [04:42<24:17,  1.39s/it]
                                                  

 16%|█▌        | 200/1250 [04:42<24:17,  1.39s/it]
 16%|█▌        | 201/1250 [04:43<24:22,  1.39s/it]
 16%|█▌        | 202/1250 [04:44<24:16,  1.39s/it]
 16%|█▌        | 203/1250 [04:46<24:12,  1.39s/it]
 16%|█▋        | 204/1250 [04:47<24:09,  1.39s/it]
 16%|█▋        | 205/1250 [04:49<24:09,  1.39s/it]
 16%|█▋        | 206/1250 [04:50<24:06,  1.39s/it]
 17%|█▋        | 207/1250 [04:51<24:04,  1.38s/it]
 17%|█▋        | 208/1250 [04:53<24:02,  1.38s/it]
 17%|█▋        | 209/1250 [04:54<24:00,  1.38s/it]
 17%|█▋        | 210/1250 [04:55<23:59,  1.38s/it]
 17%|█▋        | 211/1250 [04:57<23:57,  1.38s/it]
 17%|█▋        | 212/1250 [04:58<23:55,  1.38s/it]
 17%|█▋        | 213/1250 [05:00<23:54,  1.38s/it]
 17%|█▋        | 214/1250 [05:01<23:51,  1.38s/it]
 17%|█▋        | 215/1250 [05:02<23:52,  1.38s/it]
 17%|█▋        | 216/1250 [05:04<23:49,  1.38s/it]
 17%|█▋        | 217/1250 [05:05<23:46,  1.38s/it]
 17%|█▋        | 218/1250 [05:07<23:47,  1.38s/it]
 18%|█▊        | 219/1250 [05:08<23:46,  1.38s/it]
 18%|█▊        | 220/1250 [05:09<23:41,  1.38s/it]
 18%|█▊        | 221/1250 [05:11<23:39,  1.38s/it]
 18%|█▊        | 222/1250 [05:12<23:38,  1.38s/it]
 18%|█▊        | 223/1250 [05:13<23:38,  1.38s/it]
 18%|█▊        | 224/1250 [05:15<23:37,  1.38s/it]
 18%|█▊        | 225/1250 [05:16<23:36,  1.38s/it]
                                                  

 18%|█▊        | 225/1250 [05:16<23:36,  1.38s/it]
 18%|█▊        | 226/1250 [05:18<23:44,  1.39s/it]
 18%|█▊        | 227/1250 [05:19<23:39,  1.39s/it]
 18%|█▊        | 228/1250 [05:20<23:36,  1.39s/it]
 18%|█▊        | 229/1250 [05:22<23:33,  1.38s/it]
 18%|█▊        | 230/1250 [05:23<23:32,  1.39s/it]
 18%|█▊        | 231/1250 [05:25<23:25,  1.38s/it]
 19%|█▊        | 232/1250 [05:26<23:23,  1.38s/it]
 19%|█▊        | 233/1250 [05:27<23:24,  1.38s/it]
 19%|█▊        | 234/1250 [05:29<23:23,  1.38s/it]
 19%|█▉        | 235/1250 [05:30<23:21,  1.38s/it]
 19%|█▉        | 236/1250 [05:31<23:18,  1.38s/it]
 19%|█▉        | 237/1250 [05:33<23:21,  1.38s/it]
 19%|█▉        | 238/1250 [05:34<23:18,  1.38s/it]
 19%|█▉        | 239/1250 [05:36<23:17,  1.38s/it]
 19%|█▉        | 240/1250 [05:37<23:15,  1.38s/it]
 19%|█▉        | 241/1250 [05:38<23:16,  1.38s/it]
 19%|█▉        | 242/1250 [05:40<23:14,  1.38s/it]
 19%|█▉        | 243/1250 [05:41<23:09,  1.38s/it]
 20%|█▉        | 244/1250 [05:42<23:13,  1.39s/it]
 20%|█▉        | 245/1250 [05:44<23:07,  1.38s/it]
 20%|█▉        | 246/1250 [05:45<23:05,  1.38s/it]
 20%|█▉        | 247/1250 [05:47<23:04,  1.38s/it]
 20%|█▉        | 248/1250 [05:48<23:03,  1.38s/it]
 20%|█▉        | 249/1250 [05:49<23:00,  1.38s/it]
 20%|██        | 250/1250 [05:51<23:00,  1.38s/it]
                                                  

 20%|██        | 250/1250 [05:51<23:00,  1.38s/it]
 20%|██        | 251/1250 [05:52<23:09,  1.39s/it]
 20%|██        | 252/1250 [05:54<23:03,  1.39s/it]
 20%|██        | 253/1250 [05:55<23:00,  1.38s/it]
 20%|██        | 254/1250 [05:56<22:55,  1.38s/it]
 20%|██        | 255/1250 [05:58<22:54,  1.38s/it]
 20%|██        | 256/1250 [05:59<22:52,  1.38s/it]
 21%|██        | 257/1250 [06:00<22:51,  1.38s/it]
 21%|██        | 258/1250 [06:02<22:49,  1.38s/it]
 21%|██        | 259/1250 [06:03<22:49,  1.38s/it]
 21%|██        | 260/1250 [06:05<22:45,  1.38s/it]
 21%|██        | 261/1250 [06:06<22:45,  1.38s/it]
 21%|██        | 262/1250 [06:07<22:48,  1.39s/it]
 21%|██        | 263/1250 [06:09<22:47,  1.39s/it]
 21%|██        | 264/1250 [06:10<22:44,  1.38s/it]
 21%|██        | 265/1250 [06:12<22:41,  1.38s/it]
 21%|██▏       | 266/1250 [06:13<23:44,  1.45s/it]
 21%|██▏       | 267/1250 [06:14<23:22,  1.43s/it]
 21%|██▏       | 268/1250 [06:16<23:09,  1.41s/it]
 22%|██▏       | 269/1250 [06:17<22:57,  1.40s/it]
 22%|██▏       | 270/1250 [06:19<22:48,  1.40s/it]
 22%|██▏       | 271/1250 [06:20<22:39,  1.39s/it]
 22%|██▏       | 272/1250 [06:21<22:36,  1.39s/it]
 22%|██▏       | 273/1250 [06:23<22:33,  1.39s/it]
 22%|██▏       | 274/1250 [06:24<22:31,  1.38s/it]
 22%|██▏       | 275/1250 [06:26<22:27,  1.38s/it]
                                                  

 22%|██▏       | 275/1250 [06:26<22:27,  1.38s/it]
 22%|██▏       | 276/1250 [06:27<22:32,  1.39s/it]
 22%|██▏       | 277/1250 [06:28<22:30,  1.39s/it]
 22%|██▏       | 278/1250 [06:30<22:25,  1.38s/it]
 22%|██▏       | 279/1250 [06:31<22:22,  1.38s/it]
 22%|██▏       | 280/1250 [06:32<22:19,  1.38s/it]
 22%|██▏       | 281/1250 [06:34<22:19,  1.38s/it]
 23%|██▎       | 282/1250 [06:35<22:17,  1.38s/it]
 23%|██▎       | 283/1250 [06:37<22:16,  1.38s/it]
 23%|██▎       | 284/1250 [06:38<22:15,  1.38s/it]
 23%|██▎       | 285/1250 [06:39<22:14,  1.38s/it]
 23%|██▎       | 286/1250 [06:41<22:11,  1.38s/it]
 23%|██▎       | 287/1250 [06:42<22:11,  1.38s/it]
 23%|██▎       | 288/1250 [06:44<22:11,  1.38s/it]
 23%|██▎       | 289/1250 [06:45<22:09,  1.38s/it]
 23%|██▎       | 290/1250 [06:46<22:05,  1.38s/it]
 23%|██▎       | 291/1250 [06:48<22:06,  1.38s/it]
 23%|██▎       | 292/1250 [06:49<22:04,  1.38s/it]
 23%|██▎       | 293/1250 [06:51<22:59,  1.44s/it]
 24%|██▎       | 294/1250 [06:52<22:42,  1.42s/it]
 24%|██▎       | 295/1250 [06:53<22:28,  1.41s/it]
 24%|██▎       | 296/1250 [06:55<22:19,  1.40s/it]
 24%|██▍       | 297/1250 [06:56<22:12,  1.40s/it]
 24%|██▍       | 298/1250 [06:58<22:09,  1.40s/it]
 24%|██▍       | 299/1250 [06:59<22:05,  1.39s/it]
 24%|██▍       | 300/1250 [07:00<22:00,  1.39s/it]
                                                  

 24%|██▍       | 300/1250 [07:00<22:00,  1.39s/it]
 24%|██▍       | 301/1250 [07:02<21:59,  1.39s/it]
 24%|██▍       | 302/1250 [07:03<21:59,  1.39s/it]
 24%|██▍       | 303/1250 [07:04<21:53,  1.39s/it]
 24%|██▍       | 304/1250 [07:06<21:47,  1.38s/it]
 24%|██▍       | 305/1250 [07:07<21:47,  1.38s/it]
 24%|██▍       | 306/1250 [07:09<21:45,  1.38s/it]
 25%|██▍       | 307/1250 [07:10<21:43,  1.38s/it]
 25%|██▍       | 308/1250 [07:11<21:42,  1.38s/it]
 25%|██▍       | 309/1250 [07:13<21:44,  1.39s/it]
 25%|██▍       | 310/1250 [07:14<21:41,  1.38s/it]
 25%|██▍       | 311/1250 [07:16<21:40,  1.38s/it]
 25%|██▍       | 312/1250 [07:17<21:40,  1.39s/it]
 25%|██▌       | 313/1250 [07:18<21:39,  1.39s/it]
 25%|██▌       | 314/1250 [07:20<21:37,  1.39s/it]
 25%|██▌       | 315/1250 [07:21<21:33,  1.38s/it]
 25%|██▌       | 316/1250 [07:22<21:35,  1.39s/it]
 25%|██▌       | 317/1250 [07:24<21:29,  1.38s/it]
 25%|██▌       | 318/1250 [07:25<21:29,  1.38s/it]
 26%|██▌       | 319/1250 [07:27<21:28,  1.38s/it]
 26%|██▌       | 320/1250 [07:28<21:28,  1.39s/it]
 26%|██▌       | 321/1250 [07:29<21:23,  1.38s/it]
 26%|██▌       | 322/1250 [07:31<21:23,  1.38s/it]
 26%|██▌       | 323/1250 [07:32<21:23,  1.38s/it]
 26%|██▌       | 324/1250 [07:34<21:22,  1.38s/it]
 26%|██▌       | 325/1250 [07:35<21:18,  1.38s/it]
                                                  

 26%|██▌       | 325/1250 [07:35<21:18,  1.38s/it]
 26%|██▌       | 326/1250 [07:36<21:22,  1.39s/it]
 26%|██▌       | 327/1250 [07:38<21:18,  1.38s/it]
 26%|██▌       | 328/1250 [07:39<21:16,  1.38s/it]
 26%|██▋       | 329/1250 [07:40<21:13,  1.38s/it]
 26%|██▋       | 330/1250 [07:42<21:11,  1.38s/it]
 26%|██▋       | 331/1250 [07:43<21:13,  1.39s/it]
 27%|██▋       | 332/1250 [07:45<21:10,  1.38s/it]
 27%|██▋       | 333/1250 [07:46<21:08,  1.38s/it]
 27%|██▋       | 334/1250 [07:47<21:07,  1.38s/it]
 27%|██▋       | 335/1250 [07:49<21:02,  1.38s/it]
 27%|██▋       | 336/1250 [07:50<20:58,  1.38s/it]
 27%|██▋       | 337/1250 [07:52<20:57,  1.38s/it]
 27%|██▋       | 338/1250 [07:53<20:58,  1.38s/it]
 27%|██▋       | 339/1250 [07:54<20:57,  1.38s/it]
 27%|██▋       | 340/1250 [07:56<20:57,  1.38s/it]
 27%|██▋       | 341/1250 [07:57<20:56,  1.38s/it]
 27%|██▋       | 342/1250 [07:58<20:56,  1.38s/it]
 27%|██▋       | 343/1250 [08:00<20:54,  1.38s/it]
 28%|██▊       | 344/1250 [08:01<20:51,  1.38s/it]
 28%|██▊       | 345/1250 [08:03<20:51,  1.38s/it]
 28%|██▊       | 346/1250 [08:04<20:49,  1.38s/it]
 28%|██▊       | 347/1250 [08:05<20:48,  1.38s/it]
 28%|██▊       | 348/1250 [08:07<20:45,  1.38s/it]
 28%|██▊       | 349/1250 [08:08<20:44,  1.38s/it]
 28%|██▊       | 350/1250 [08:09<20:43,  1.38s/it]
                                                  

 28%|██▊       | 350/1250 [08:10<20:43,  1.38s/it]
 28%|██▊       | 351/1250 [08:11<20:51,  1.39s/it]
 28%|██▊       | 352/1250 [08:12<20:48,  1.39s/it]
 28%|██▊       | 353/1250 [08:14<20:46,  1.39s/it]
 28%|██▊       | 354/1250 [08:15<20:42,  1.39s/it]
 28%|██▊       | 355/1250 [08:16<20:39,  1.38s/it]
 28%|██▊       | 356/1250 [08:18<20:37,  1.38s/it]
 29%|██▊       | 357/1250 [08:19<20:34,  1.38s/it]
 29%|██▊       | 358/1250 [08:21<20:32,  1.38s/it]
 29%|██▊       | 359/1250 [08:22<20:31,  1.38s/it]
 29%|██▉       | 360/1250 [08:23<20:31,  1.38s/it]
 29%|██▉       | 361/1250 [08:25<20:28,  1.38s/it]
 29%|██▉       | 362/1250 [08:26<20:27,  1.38s/it]
 29%|██▉       | 363/1250 [08:27<20:27,  1.38s/it]
 29%|██▉       | 364/1250 [08:29<20:25,  1.38s/it]
 29%|██▉       | 365/1250 [08:30<20:23,  1.38s/it]
 29%|██▉       | 366/1250 [08:32<21:15,  1.44s/it]
 29%|██▉       | 367/1250 [08:33<21:01,  1.43s/it]
 29%|██▉       | 368/1250 [08:35<20:47,  1.41s/it]
 30%|██▉       | 369/1250 [08:36<20:38,  1.41s/it]
 30%|██▉       | 370/1250 [08:37<20:32,  1.40s/it]
 30%|██▉       | 371/1250 [08:39<20:24,  1.39s/it]
 30%|██▉       | 372/1250 [08:40<20:19,  1.39s/it]
 30%|██▉       | 373/1250 [08:42<21:11,  1.45s/it]
 30%|██▉       | 374/1250 [08:43<20:53,  1.43s/it]
 30%|███       | 375/1250 [08:45<20:39,  1.42s/it]
                                                  

 30%|███       | 375/1250 [08:45<20:39,  1.42s/it]
 30%|███       | 376/1250 [08:46<20:35,  1.41s/it]
 30%|███       | 377/1250 [08:47<20:24,  1.40s/it]
 30%|███       | 378/1250 [08:49<20:19,  1.40s/it]
 30%|███       | 379/1250 [08:50<20:12,  1.39s/it]
 30%|███       | 380/1250 [08:51<20:08,  1.39s/it]
 30%|███       | 381/1250 [08:53<20:06,  1.39s/it]
 31%|███       | 382/1250 [08:54<20:01,  1.38s/it]
 31%|███       | 383/1250 [08:56<20:00,  1.38s/it]
 31%|███       | 384/1250 [08:57<19:57,  1.38s/it]
 31%|███       | 385/1250 [08:58<19:57,  1.38s/it]
 31%|███       | 386/1250 [09:00<19:54,  1.38s/it]
 31%|███       | 387/1250 [09:01<19:53,  1.38s/it]
 31%|███       | 388/1250 [09:02<19:52,  1.38s/it]
 31%|███       | 389/1250 [09:04<19:51,  1.38s/it]
 31%|███       | 390/1250 [09:05<19:50,  1.38s/it]
 31%|███▏      | 391/1250 [09:07<19:49,  1.38s/it]
 31%|███▏      | 392/1250 [09:08<19:46,  1.38s/it]
 31%|███▏      | 393/1250 [09:09<19:45,  1.38s/it]
 32%|███▏      | 394/1250 [09:11<19:42,  1.38s/it]
 32%|███▏      | 395/1250 [09:12<19:40,  1.38s/it]
 32%|███▏      | 396/1250 [09:14<19:40,  1.38s/it]
 32%|███▏      | 397/1250 [09:15<19:38,  1.38s/it]
 32%|███▏      | 398/1250 [09:16<19:37,  1.38s/it]
 32%|███▏      | 399/1250 [09:18<19:37,  1.38s/it]
 32%|███▏      | 400/1250 [09:19<19:33,  1.38s/it]
                                                  

 32%|███▏      | 400/1250 [09:19<19:33,  1.38s/it]
 32%|███▏      | 401/1250 [09:20<19:40,  1.39s/it]
 32%|███▏      | 402/1250 [09:22<19:34,  1.39s/it]
 32%|███▏      | 403/1250 [09:23<19:34,  1.39s/it]
 32%|███▏      | 404/1250 [09:25<19:33,  1.39s/it]
 32%|███▏      | 405/1250 [09:26<19:29,  1.38s/it]
 32%|███▏      | 406/1250 [09:27<19:30,  1.39s/it]
 33%|███▎      | 407/1250 [09:29<19:27,  1.39s/it]
 33%|███▎      | 408/1250 [09:30<19:26,  1.39s/it]
 33%|███▎      | 409/1250 [09:32<19:24,  1.38s/it]
 33%|███▎      | 410/1250 [09:33<19:25,  1.39s/it]
 33%|███▎      | 411/1250 [09:34<19:22,  1.39s/it]
 33%|███▎      | 412/1250 [09:36<19:18,  1.38s/it]
 33%|███▎      | 413/1250 [09:37<19:16,  1.38s/it]
 33%|███▎      | 414/1250 [09:38<19:17,  1.38s/it]
 33%|███▎      | 415/1250 [09:40<19:14,  1.38s/it]
 33%|███▎      | 416/1250 [09:41<19:11,  1.38s/it]
 33%|███▎      | 417/1250 [09:43<19:12,  1.38s/it]
 33%|███▎      | 418/1250 [09:44<19:10,  1.38s/it]
 34%|███▎      | 419/1250 [09:45<19:08,  1.38s/it]
 34%|███▎      | 420/1250 [09:47<19:07,  1.38s/it]
 34%|███▎      | 421/1250 [09:48<19:07,  1.38s/it]
 34%|███▍      | 422/1250 [09:50<19:02,  1.38s/it]
 34%|███▍      | 423/1250 [09:51<19:01,  1.38s/it]
 34%|███▍      | 424/1250 [09:52<19:02,  1.38s/it]
 34%|███▍      | 425/1250 [09:54<18:59,  1.38s/it]
                                                  

 34%|███▍      | 425/1250 [09:54<18:59,  1.38s/it]
 34%|███▍      | 426/1250 [09:55<19:05,  1.39s/it]
 34%|███▍      | 427/1250 [09:56<19:01,  1.39s/it]
 34%|███▍      | 428/1250 [09:58<19:00,  1.39s/it]
 34%|███▍      | 429/1250 [09:59<18:57,  1.39s/it]
 34%|███▍      | 430/1250 [10:01<18:53,  1.38s/it]
 34%|███▍      | 431/1250 [10:02<18:50,  1.38s/it]
 35%|███▍      | 432/1250 [10:03<18:50,  1.38s/it]
 35%|███▍      | 433/1250 [10:05<18:45,  1.38s/it]
 35%|███▍      | 434/1250 [10:06<18:44,  1.38s/it]
 35%|███▍      | 435/1250 [10:08<18:44,  1.38s/it]
 35%|███▍      | 436/1250 [10:09<18:43,  1.38s/it]
 35%|███▍      | 437/1250 [10:10<18:41,  1.38s/it]
 35%|███▌      | 438/1250 [10:12<18:40,  1.38s/it]
 35%|███▌      | 439/1250 [10:13<18:40,  1.38s/it]
 35%|███▌      | 440/1250 [10:14<18:37,  1.38s/it]
 35%|███▌      | 441/1250 [10:16<18:35,  1.38s/it]
 35%|███▌      | 442/1250 [10:17<18:34,  1.38s/it]
 35%|███▌      | 443/1250 [10:19<18:35,  1.38s/it]
 36%|███▌      | 444/1250 [10:20<18:33,  1.38s/it]
 36%|███▌      | 445/1250 [10:21<18:29,  1.38s/it]
 36%|███▌      | 446/1250 [10:23<18:27,  1.38s/it]
 36%|███▌      | 447/1250 [10:24<18:27,  1.38s/it]
 36%|███▌      | 448/1250 [10:26<19:17,  1.44s/it]
 36%|███▌      | 449/1250 [10:27<19:02,  1.43s/it]
 36%|███▌      | 450/1250 [10:28<18:51,  1.41s/it]
                                                  

 36%|███▌      | 450/1250 [10:28<18:51,  1.41s/it]
 36%|███▌      | 451/1250 [10:30<18:46,  1.41s/it]
 36%|███▌      | 452/1250 [10:31<18:38,  1.40s/it]
 36%|███▌      | 453/1250 [10:33<18:35,  1.40s/it]
 36%|███▋      | 454/1250 [10:34<18:28,  1.39s/it]
 36%|███▋      | 455/1250 [10:35<18:23,  1.39s/it]
 36%|███▋      | 456/1250 [10:37<18:17,  1.38s/it]
 37%|███▋      | 457/1250 [10:38<18:18,  1.38s/it]
 37%|███▋      | 458/1250 [10:39<18:13,  1.38s/it]
 37%|███▋      | 459/1250 [10:41<18:11,  1.38s/it]
 37%|███▋      | 460/1250 [10:42<18:09,  1.38s/it]
 37%|███▋      | 461/1250 [10:44<18:09,  1.38s/it]
 37%|███▋      | 462/1250 [10:45<18:08,  1.38s/it]
 37%|███▋      | 463/1250 [10:46<18:07,  1.38s/it]
 37%|███▋      | 464/1250 [10:48<18:07,  1.38s/it]
 37%|███▋      | 465/1250 [10:49<18:05,  1.38s/it]
 37%|███▋      | 466/1250 [10:51<18:02,  1.38s/it]
 37%|███▋      | 467/1250 [10:52<18:00,  1.38s/it]
 37%|███▋      | 468/1250 [10:53<17:59,  1.38s/it]
 38%|███▊      | 469/1250 [10:55<17:59,  1.38s/it]
 38%|███▊      | 470/1250 [10:56<17:58,  1.38s/it]
 38%|███▊      | 471/1250 [10:57<17:57,  1.38s/it]
 38%|███▊      | 472/1250 [10:59<17:56,  1.38s/it]
 38%|███▊      | 473/1250 [11:00<17:53,  1.38s/it]
 38%|███▊      | 474/1250 [11:02<17:51,  1.38s/it]
 38%|███▊      | 475/1250 [11:03<17:52,  1.38s/it]
                                                  

 38%|███▊      | 475/1250 [11:03<17:52,  1.38s/it]
 38%|███▊      | 476/1250 [11:04<17:53,  1.39s/it]
 38%|███▊      | 477/1250 [11:06<17:51,  1.39s/it]
 38%|███▊      | 478/1250 [11:07<17:46,  1.38s/it]
 38%|███▊      | 479/1250 [11:09<17:45,  1.38s/it]
 38%|███▊      | 480/1250 [11:10<17:41,  1.38s/it]
 38%|███▊      | 481/1250 [11:11<17:38,  1.38s/it]
 39%|███▊      | 482/1250 [11:13<17:38,  1.38s/it]
 39%|███▊      | 483/1250 [11:14<17:38,  1.38s/it]
 39%|███▊      | 484/1250 [11:15<17:34,  1.38s/it]
 39%|███▉      | 485/1250 [11:17<17:33,  1.38s/it]
 39%|███▉      | 486/1250 [11:18<17:35,  1.38s/it]
 39%|███▉      | 487/1250 [11:20<17:33,  1.38s/it]
 39%|███▉      | 488/1250 [11:21<17:31,  1.38s/it]
 39%|███▉      | 489/1250 [11:22<17:32,  1.38s/it]
 39%|███▉      | 490/1250 [11:24<17:31,  1.38s/it]
 39%|███▉      | 491/1250 [11:25<17:30,  1.38s/it]
 39%|███▉      | 492/1250 [11:26<17:28,  1.38s/it]
 39%|███▉      | 493/1250 [11:28<17:28,  1.38s/it]
 40%|███▉      | 494/1250 [11:29<17:25,  1.38s/it]
 40%|███▉      | 495/1250 [11:31<17:22,  1.38s/it]
 40%|███▉      | 496/1250 [11:32<17:22,  1.38s/it]
 40%|███▉      | 497/1250 [11:33<17:20,  1.38s/it]
 40%|███▉      | 498/1250 [11:35<17:19,  1.38s/it]
 40%|███▉      | 499/1250 [11:36<17:17,  1.38s/it]
 40%|████      | 500/1250 [11:38<17:16,  1.38s/it]
                                                  

 40%|████      | 500/1250 [11:38<17:16,  1.38s/it]
 40%|████      | 501/1250 [11:39<17:23,  1.39s/it]
 40%|████      | 502/1250 [11:40<17:20,  1.39s/it]
 40%|████      | 503/1250 [11:42<17:17,  1.39s/it]
 40%|████      | 504/1250 [11:43<17:13,  1.39s/it]
 40%|████      | 505/1250 [11:44<17:09,  1.38s/it]
 40%|████      | 506/1250 [11:46<17:08,  1.38s/it]
 41%|████      | 507/1250 [11:47<17:04,  1.38s/it]
 41%|████      | 508/1250 [11:49<17:03,  1.38s/it]
 41%|████      | 509/1250 [11:50<17:01,  1.38s/it]
 41%|████      | 510/1250 [11:51<17:01,  1.38s/it]
 41%|████      | 511/1250 [11:53<17:00,  1.38s/it]
 41%|████      | 512/1250 [11:54<16:58,  1.38s/it]
 41%|████      | 513/1250 [11:55<16:58,  1.38s/it]
 41%|████      | 514/1250 [11:57<16:57,  1.38s/it]
 41%|████      | 515/1250 [11:58<16:55,  1.38s/it]
 41%|████▏     | 516/1250 [12:00<16:53,  1.38s/it]
 41%|████▏     | 517/1250 [12:01<16:51,  1.38s/it]
 41%|████▏     | 518/1250 [12:02<16:50,  1.38s/it]
 42%|████▏     | 519/1250 [12:04<16:49,  1.38s/it]
 42%|████▏     | 520/1250 [12:05<16:48,  1.38s/it]
 42%|████▏     | 521/1250 [12:07<16:47,  1.38s/it]
 42%|████▏     | 522/1250 [12:08<16:46,  1.38s/it]
 42%|████▏     | 523/1250 [12:09<16:44,  1.38s/it]
 42%|████▏     | 524/1250 [12:11<16:42,  1.38s/it]
 42%|████▏     | 525/1250 [12:12<16:41,  1.38s/it]
                                                  

 42%|████▏     | 525/1250 [12:12<16:41,  1.38s/it]
 42%|████▏     | 526/1250 [12:13<16:45,  1.39s/it]
 42%|████▏     | 527/1250 [12:15<16:42,  1.39s/it]
 42%|████▏     | 528/1250 [12:16<16:40,  1.39s/it]
 42%|████▏     | 529/1250 [12:18<16:37,  1.38s/it]
 42%|████▏     | 530/1250 [12:19<16:35,  1.38s/it]
 42%|████▏     | 531/1250 [12:20<16:35,  1.39s/it]
 43%|████▎     | 532/1250 [12:22<16:33,  1.38s/it]
 43%|████▎     | 533/1250 [12:23<16:31,  1.38s/it]
 43%|████▎     | 534/1250 [12:25<16:30,  1.38s/it]
 43%|████▎     | 535/1250 [12:26<16:28,  1.38s/it]
 43%|████▎     | 536/1250 [12:27<16:25,  1.38s/it]
 43%|████▎     | 537/1250 [12:29<16:23,  1.38s/it]
 43%|████▎     | 538/1250 [12:30<16:22,  1.38s/it]
 43%|████▎     | 539/1250 [12:31<16:20,  1.38s/it]
 43%|████▎     | 540/1250 [12:33<16:17,  1.38s/it]
 43%|████▎     | 541/1250 [12:34<16:17,  1.38s/it]
 43%|████▎     | 542/1250 [12:36<16:16,  1.38s/it]
 43%|████▎     | 543/1250 [12:37<16:15,  1.38s/it]
 44%|████▎     | 544/1250 [12:38<16:13,  1.38s/it]
 44%|████▎     | 545/1250 [12:40<16:12,  1.38s/it]
 44%|████▎     | 546/1250 [12:41<16:11,  1.38s/it]
 44%|████▍     | 547/1250 [12:42<16:09,  1.38s/it]
 44%|████▍     | 548/1250 [12:44<16:08,  1.38s/it]
 44%|████▍     | 549/1250 [12:45<16:06,  1.38s/it]
 44%|████▍     | 550/1250 [12:47<16:06,  1.38s/it]
                                                  

 44%|████▍     | 550/1250 [12:47<16:06,  1.38s/it]
 44%|████▍     | 551/1250 [12:48<16:06,  1.38s/it]
 44%|████▍     | 552/1250 [12:49<16:05,  1.38s/it]
 44%|████▍     | 553/1250 [12:51<16:04,  1.38s/it]
 44%|████▍     | 554/1250 [12:52<16:15,  1.40s/it]
 44%|████▍     | 555/1250 [12:54<16:09,  1.39s/it]
 44%|████▍     | 556/1250 [12:55<16:05,  1.39s/it]
 45%|████▍     | 557/1250 [12:56<16:02,  1.39s/it]
 45%|████▍     | 558/1250 [12:58<16:40,  1.45s/it]
 45%|████▍     | 559/1250 [12:59<16:24,  1.42s/it]
 45%|████▍     | 560/1250 [13:01<16:13,  1.41s/it]
 45%|████▍     | 561/1250 [13:02<16:07,  1.40s/it]
 45%|████▍     | 562/1250 [13:03<16:02,  1.40s/it]
 45%|████▌     | 563/1250 [13:05<15:57,  1.39s/it]
 45%|████▌     | 564/1250 [13:06<15:53,  1.39s/it]
 45%|████▌     | 565/1250 [13:08<15:50,  1.39s/it]
 45%|████▌     | 566/1250 [13:09<15:47,  1.38s/it]
 45%|████▌     | 567/1250 [13:10<15:44,  1.38s/it]
 45%|████▌     | 568/1250 [13:12<15:43,  1.38s/it]
 46%|████▌     | 569/1250 [13:13<15:41,  1.38s/it]
 46%|████▌     | 570/1250 [13:15<15:40,  1.38s/it]
 46%|████▌     | 571/1250 [13:16<15:38,  1.38s/it]
 46%|████▌     | 572/1250 [13:17<15:35,  1.38s/it]
 46%|████▌     | 573/1250 [13:19<15:35,  1.38s/it]
 46%|████▌     | 574/1250 [13:20<15:34,  1.38s/it]
 46%|████▌     | 575/1250 [13:21<15:32,  1.38s/it]
                                                  

 46%|████▌     | 575/1250 [13:21<15:32,  1.38s/it]
 46%|████▌     | 576/1250 [13:23<15:32,  1.38s/it]
 46%|████▌     | 577/1250 [13:24<15:30,  1.38s/it]
 46%|████▌     | 578/1250 [13:26<15:29,  1.38s/it]
 46%|████▋     | 579/1250 [13:27<15:27,  1.38s/it]
 46%|████▋     | 580/1250 [13:28<15:27,  1.38s/it]
 46%|████▋     | 581/1250 [13:30<15:24,  1.38s/it]
 47%|████▋     | 582/1250 [13:31<15:23,  1.38s/it]
 47%|████▋     | 583/1250 [13:32<15:22,  1.38s/it]
 47%|████▋     | 584/1250 [13:34<15:20,  1.38s/it]
 47%|████▋     | 585/1250 [13:35<15:19,  1.38s/it]
 47%|████▋     | 586/1250 [13:37<15:17,  1.38s/it]
 47%|████▋     | 587/1250 [13:38<15:14,  1.38s/it]
 47%|████▋     | 588/1250 [13:39<15:14,  1.38s/it]
 47%|████▋     | 589/1250 [13:41<15:13,  1.38s/it]
 47%|████▋     | 590/1250 [13:42<15:12,  1.38s/it]
 47%|████▋     | 591/1250 [13:44<15:10,  1.38s/it]
 47%|████▋     | 592/1250 [13:45<15:09,  1.38s/it]
 47%|████▋     | 593/1250 [13:46<15:07,  1.38s/it]
 48%|████▊     | 594/1250 [13:48<15:07,  1.38s/it]
 48%|████▊     | 595/1250 [13:49<15:06,  1.38s/it]
 48%|████▊     | 596/1250 [13:50<15:05,  1.38s/it]
 48%|████▊     | 597/1250 [13:52<15:03,  1.38s/it]
 48%|████▊     | 598/1250 [13:53<15:02,  1.38s/it]
 48%|████▊     | 599/1250 [13:55<15:01,  1.38s/it]
 48%|████▊     | 600/1250 [13:56<14:59,  1.38s/it]
                                                  

 48%|████▊     | 600/1250 [13:56<14:59,  1.38s/it]
 48%|████▊     | 601/1250 [13:57<15:03,  1.39s/it]
 48%|████▊     | 602/1250 [13:59<14:59,  1.39s/it]
 48%|████▊     | 603/1250 [14:00<14:57,  1.39s/it]
 48%|████▊     | 604/1250 [14:02<14:55,  1.39s/it]
 48%|████▊     | 605/1250 [14:03<14:54,  1.39s/it]
 48%|████▊     | 606/1250 [14:04<14:52,  1.39s/it]
 49%|████▊     | 607/1250 [14:06<14:49,  1.38s/it]
 49%|████▊     | 608/1250 [14:07<14:47,  1.38s/it]
 49%|████▊     | 609/1250 [14:08<14:46,  1.38s/it]
 49%|████▉     | 610/1250 [14:10<14:45,  1.38s/it]
 49%|████▉     | 611/1250 [14:11<14:44,  1.38s/it]
 49%|████▉     | 612/1250 [14:13<14:43,  1.38s/it]
 49%|████▉     | 613/1250 [14:14<14:41,  1.38s/it]
 49%|████▉     | 614/1250 [14:15<14:39,  1.38s/it]
 49%|████▉     | 615/1250 [14:17<14:38,  1.38s/it]
 49%|████▉     | 616/1250 [14:18<14:38,  1.39s/it]
 49%|████▉     | 617/1250 [14:20<14:38,  1.39s/it]
 49%|████▉     | 618/1250 [14:21<14:37,  1.39s/it]
 50%|████▉     | 619/1250 [14:22<14:36,  1.39s/it]
 50%|████▉     | 620/1250 [14:24<14:34,  1.39s/it]
 50%|████▉     | 621/1250 [14:25<14:32,  1.39s/it]
 50%|████▉     | 622/1250 [14:26<14:30,  1.39s/it]
 50%|████▉     | 623/1250 [14:28<14:28,  1.39s/it]
 50%|████▉     | 624/1250 [14:29<14:26,  1.38s/it]
 50%|█████     | 625/1250 [14:31<14:24,  1.38s/it]
                                                  

 50%|█████     | 625/1250 [14:31<14:24,  1.38s/it]
 50%|█████     | 626/1250 [14:32<14:29,  1.39s/it]
 50%|█████     | 627/1250 [14:33<14:25,  1.39s/it]
 50%|█████     | 628/1250 [14:35<14:22,  1.39s/it]
 50%|█████     | 629/1250 [14:36<14:20,  1.39s/it]
 50%|█████     | 630/1250 [14:38<14:18,  1.38s/it]
 50%|█████     | 631/1250 [14:39<14:16,  1.38s/it]
 51%|█████     | 632/1250 [14:40<14:14,  1.38s/it]
 51%|█████     | 633/1250 [14:42<14:13,  1.38s/it]
 51%|█████     | 634/1250 [14:43<14:13,  1.39s/it]
 51%|█████     | 635/1250 [14:44<14:10,  1.38s/it]
 51%|█████     | 636/1250 [14:46<14:09,  1.38s/it]
 51%|█████     | 637/1250 [14:47<14:07,  1.38s/it]
 51%|█████     | 638/1250 [14:49<14:05,  1.38s/it]
 51%|█████     | 639/1250 [14:50<14:05,  1.38s/it]
 51%|█████     | 640/1250 [14:52<14:42,  1.45s/it]
 51%|█████▏    | 641/1250 [14:53<14:29,  1.43s/it]
 51%|█████▏    | 642/1250 [14:54<14:19,  1.41s/it]
 51%|█████▏    | 643/1250 [14:56<14:12,  1.40s/it]
 52%|█████▏    | 644/1250 [14:57<14:07,  1.40s/it]
 52%|█████▏    | 645/1250 [14:59<14:03,  1.39s/it]
 52%|█████▏    | 646/1250 [15:00<14:00,  1.39s/it]
 52%|█████▏    | 647/1250 [15:01<13:58,  1.39s/it]
 52%|█████▏    | 648/1250 [15:03<13:56,  1.39s/it]
 52%|█████▏    | 649/1250 [15:04<13:54,  1.39s/it]
 52%|█████▏    | 650/1250 [15:05<13:51,  1.39s/it]
                                                  

 52%|█████▏    | 650/1250 [15:05<13:51,  1.39s/it]
 52%|█████▏    | 651/1250 [15:07<13:55,  1.40s/it]
 52%|█████▏    | 652/1250 [15:08<13:52,  1.39s/it]
 52%|█████▏    | 653/1250 [15:10<13:49,  1.39s/it]
 52%|█████▏    | 654/1250 [15:11<13:48,  1.39s/it]
 52%|█████▏    | 655/1250 [15:12<13:46,  1.39s/it]
 52%|█████▏    | 656/1250 [15:14<13:43,  1.39s/it]
 53%|█████▎    | 657/1250 [15:15<13:42,  1.39s/it]
 53%|█████▎    | 658/1250 [15:17<13:39,  1.38s/it]
 53%|█████▎    | 659/1250 [15:18<13:37,  1.38s/it]
 53%|█████▎    | 660/1250 [15:19<13:36,  1.38s/it]
 53%|█████▎    | 661/1250 [15:21<13:34,  1.38s/it]
 53%|█████▎    | 662/1250 [15:22<13:33,  1.38s/it]
 53%|█████▎    | 663/1250 [15:23<13:33,  1.39s/it]
 53%|█████▎    | 664/1250 [15:25<14:08,  1.45s/it]
 53%|█████▎    | 665/1250 [15:26<13:54,  1.43s/it]
 53%|█████▎    | 666/1250 [15:28<13:47,  1.42s/it]
 53%|█████▎    | 667/1250 [15:29<13:41,  1.41s/it]
 53%|█████▎    | 668/1250 [15:31<13:35,  1.40s/it]
 54%|█████▎    | 669/1250 [15:32<13:30,  1.40s/it]
 54%|█████▎    | 670/1250 [15:33<13:26,  1.39s/it]
 54%|█████▎    | 671/1250 [15:35<13:24,  1.39s/it]
 54%|█████▍    | 672/1250 [15:36<13:21,  1.39s/it]
 54%|█████▍    | 673/1250 [15:38<13:20,  1.39s/it]
 54%|█████▍    | 674/1250 [15:39<13:18,  1.39s/it]
 54%|█████▍    | 675/1250 [15:40<13:16,  1.39s/it]
                                                  

 54%|█████▍    | 675/1250 [15:40<13:16,  1.39s/it]
 54%|█████▍    | 676/1250 [15:42<13:20,  1.40s/it]
 54%|█████▍    | 677/1250 [15:43<13:17,  1.39s/it]
 54%|█████▍    | 678/1250 [15:44<13:14,  1.39s/it]
 54%|█████▍    | 679/1250 [15:46<13:11,  1.39s/it]
 54%|█████▍    | 680/1250 [15:47<13:10,  1.39s/it]
 54%|█████▍    | 681/1250 [15:49<13:08,  1.38s/it]
 55%|█████▍    | 682/1250 [15:50<13:04,  1.38s/it]
 55%|█████▍    | 683/1250 [15:51<13:03,  1.38s/it]
 55%|█████▍    | 684/1250 [15:53<13:01,  1.38s/it]
 55%|█████▍    | 685/1250 [15:54<13:00,  1.38s/it]
 55%|█████▍    | 686/1250 [15:56<12:57,  1.38s/it]
 55%|█████▍    | 687/1250 [15:57<12:57,  1.38s/it]
 55%|█████▌    | 688/1250 [15:58<12:56,  1.38s/it]
 55%|█████▌    | 689/1250 [16:00<12:55,  1.38s/it]
 55%|█████▌    | 690/1250 [16:01<12:54,  1.38s/it]
 55%|█████▌    | 691/1250 [16:02<12:53,  1.38s/it]
 55%|█████▌    | 692/1250 [16:04<12:51,  1.38s/it]
 55%|█████▌    | 693/1250 [16:05<12:51,  1.38s/it]
 56%|█████▌    | 694/1250 [16:07<12:49,  1.38s/it]
 56%|█████▌    | 695/1250 [16:08<12:46,  1.38s/it]
 56%|█████▌    | 696/1250 [16:09<12:59,  1.41s/it]
 56%|█████▌    | 697/1250 [16:11<12:52,  1.40s/it]
 56%|█████▌    | 698/1250 [16:12<12:48,  1.39s/it]
 56%|█████▌    | 699/1250 [16:14<12:45,  1.39s/it]
 56%|█████▌    | 700/1250 [16:15<12:43,  1.39s/it]
                                                  

 56%|█████▌    | 700/1250 [16:15<12:43,  1.39s/it]
 56%|█████▌    | 701/1250 [16:16<12:44,  1.39s/it]
 56%|█████▌    | 702/1250 [16:18<12:41,  1.39s/it]
 56%|█████▌    | 703/1250 [16:19<12:38,  1.39s/it]
 56%|█████▋    | 704/1250 [16:21<12:36,  1.39s/it]
 56%|█████▋    | 705/1250 [16:22<12:34,  1.38s/it]
 56%|█████▋    | 706/1250 [16:23<12:32,  1.38s/it]
 57%|█████▋    | 707/1250 [16:25<12:29,  1.38s/it]
 57%|█████▋    | 708/1250 [16:26<12:28,  1.38s/it]
 57%|█████▋    | 709/1250 [16:27<12:24,  1.38s/it]
 57%|█████▋    | 710/1250 [16:29<12:24,  1.38s/it]
 57%|█████▋    | 711/1250 [16:30<12:23,  1.38s/it]
 57%|█████▋    | 712/1250 [16:32<12:21,  1.38s/it]
 57%|█████▋    | 713/1250 [16:33<12:20,  1.38s/it]
 57%|█████▋    | 714/1250 [16:34<12:19,  1.38s/it]
 57%|█████▋    | 715/1250 [16:36<12:17,  1.38s/it]
 57%|█████▋    | 716/1250 [16:37<12:16,  1.38s/it]