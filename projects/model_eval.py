import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
import json
from transformers import  AutoTokenizer, AutoModelForCausalLM
import torch
import os
from transformers import StoppingCriteria, StoppingCriteriaList
from mergoo.models.modeling_mistral import MistralForCausalLM
from datasets import load_dataset

class StopOnToken(StoppingCriteria):
    def __init__(self, stop_token_ids):
        self.stop_token_ids = stop_token_ids

    def __call__(self, input_ids, scores, **kwargs):
        return any(input_ids[0, -len(ids):].tolist() == ids for ids in self.stop_token_ids)

# --- Configuration ---
# Replace this with the path to your fully fine-tuned model
# finetuned_model_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/data/output/mistral_lora_moe_drug_abuse_pubmedqau_2/checkpoint-1955"  
# finetuned_model_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/data/output/mistral_lora_moe_drug_abuse_pubmedqau_3/checkpoint-3910"  
# finetuned_model_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/data/output/mistral_lora_moe_drug_abuse_pubmedqau_top1/checkpoint-3910"  
finetuned_model_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/mergoo/projects/data/mistral_lora_moe_cybersecurity_top1"
# finetuned_model_path = "mistralai/Mistral-7B-v0.1"
# test_data_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/pubmed/pubmed_qa/ori_pqau_instruction_response_10k_sample.json"
# test_data_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/4_cleaned_330k_train_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substance.json"
# test_data_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/pku_saferlhf_processed/test_unsafe_cybercrime.json"
# test_data_path = "fwnlp/self-instruct-safety-alignment"
# test_data_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/MoE-PEFT/datasets/beavertail/2_cleaned_330k_test_beaverTails_safe_gpt4_qtype4_drug_abuse_weapons_banned_substanc.json"
test_data_path = "/home/<USER>/m/maryam.hashemzadeh/scratch/saftly/datasets_processing/pku_saferlhf_processed/test_unsafe_cybercrime.json"
local=True

# output_json_path = "evals_output/fine_tuned_model_responses_safe_drug_abuse_pubmedqau_top1.json"
# output_json_path = "evals_output/base_model_drug_abuse.json"
output_json_path = "evals_output/moe_model_cybercrime.json"
device = "cuda" if torch.cuda.is_available() else "cpu"

# --- Load tokenizer and fine-tuned model directly ---
# tokenizer = AutoTokenizer.from_pretrained(finetuned_model_path, use_fast=True)
tokenizer = AutoTokenizer.from_pretrained(finetuned_model_path)
# model = AutoModelForCausalLM.from_pretrained(
#     finetuned_model_path,
#     torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
#     device_map="auto"
# )

model = MistralForCausalLM.from_pretrained(
            finetuned_model_path,
            torch_dtype=torch.bfloat16,
        )



model.eval()

# Define stop sequence token ids
stop_str = "\n###"
stop_token_ids = [tokenizer.encode(stop_str, add_special_tokens=False)]
stop_criteria = StoppingCriteriaList([StopOnToken(stop_token_ids)])

# --- Load test data ---
if local:
    with open(test_data_path, "r", encoding="utf-8") as f:
        test_data = json.load(f)
else:
    test_data = load_dataset(test_data_path)['test']

# --- Generate responses ---
results = []
# for example in test_data[:10]:
for i, example in enumerate(test_data):
    if i >= 100:  # Limit to first 10 examples for testing
        break
    # Handle both "instruction" and "prompt" fields
    instruction = example.get("instruction", "") or example.get("prompt", "")
    if not instruction.strip():
        example["response"] = ""
        continue

    prompt = f"### Question: {instruction}\n### Answer:"
    inputs = tokenizer(prompt, return_tensors="pt").to(model.device)

    with torch.no_grad():
        output_ids = model.generate(
            **inputs,
            max_new_tokens=150,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            pad_token_id=tokenizer.eos_token_id,
            eos_token_id=tokenizer.eos_token_id,
            stopping_criteria=stop_criteria,
        )

    output_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)

    if output_text.startswith(prompt):
        response = output_text[len(prompt):].strip()
    else:
        response = output_text.strip()

    response = response.split("\n\n")[0].strip()

    results.append({
        "instruction": instruction,
        "response": response
    })
    # print(f" *********** Instruction: {instruction}\nResponse: {response}\n")

# --- Save to JSON ---
os.makedirs("evals_output", exist_ok=True)
with open(output_json_path, "w", encoding="utf-8") as f:
    json.dump(results, f, indent=2, ensure_ascii=False)

print(f"Saved results to {output_json_path}")
